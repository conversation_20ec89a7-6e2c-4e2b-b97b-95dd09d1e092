// Progress Tracking and Analytics
class ProgressManager {
    constructor() {
        this.charts = {};
        this.currentPeriod = 'week';
        this.init();
    }

    init() {
        this.setupProgressTab();
        this.loadRecommendations();
    }

    setupProgressTab() {
        // Initialize charts when progress tab is first accessed
        const progressTab = document.querySelector('[data-tab="progress"]');
        if (progressTab) {
            progressTab.addEventListener('click', () => {
                setTimeout(() => {
                    this.initializeCharts();
                    this.loadRecommendations();
                }, 100);
            });
        }
    }

    initializeCharts() {
        this.createVolumeChart();
        this.createStrengthChart();
    }

    createVolumeChart() {
        const canvas = document.getElementById('volume-chart');
        if (!canvas || this.charts.volume) return;

        const ctx = canvas.getContext('2d');
        const progressData = StorageManager.getProgressData(this.currentPeriod);

        // Prepare data for volume chart
        const labels = progressData.volumeData.map(data => {
            const date = new Date(data.date);
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });

        const volumeValues = progressData.volumeData.map(data => data.volume);
        const setsValues = progressData.volumeData.map(data => data.sets);

        this.charts.volume = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Total Volume (kg)',
                        data: volumeValues,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Total Sets',
                        data: setsValues,
                        borderColor: '#764ba2',
                        backgroundColor: 'rgba(118, 75, 162, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.4,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'Training Volume Over Time'
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Volume (kg)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Sets'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                elements: {
                    point: {
                        radius: 6,
                        hoverRadius: 8
                    }
                }
            }
        });
    }

    createStrengthChart() {
        const canvas = document.getElementById('strength-chart');
        if (!canvas || this.charts.strength) return;

        const ctx = canvas.getContext('2d');
        const progressData = StorageManager.getProgressData(this.currentPeriod);

        // Group strength data by exercise
        const exerciseData = {};
        progressData.strengthData.forEach(data => {
            if (!exerciseData[data.exercise]) {
                exerciseData[data.exercise] = [];
            }
            exerciseData[data.exercise].push({
                x: new Date(data.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
                y: data.maxWeight
            });
        });

        // Create datasets for each exercise
        const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'];
        const datasets = Object.keys(exerciseData).map((exercise, index) => ({
            label: exercise,
            data: exerciseData[exercise],
            borderColor: colors[index % colors.length],
            backgroundColor: colors[index % colors.length] + '20',
            borderWidth: 3,
            fill: false,
            tension: 0.4
        }));

        this.charts.strength = new Chart(ctx, {
            type: 'line',
            data: {
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'Strength Progression'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Weight (kg)'
                        }
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                elements: {
                    point: {
                        radius: 6,
                        hoverRadius: 8
                    }
                }
            }
        });
    }

    updateCharts(period) {
        this.currentPeriod = period;
        
        // Destroy existing charts
        if (this.charts.volume) {
            this.charts.volume.destroy();
            this.charts.volume = null;
        }
        
        if (this.charts.strength) {
            this.charts.strength.destroy();
            this.charts.strength = null;
        }

        // Recreate charts with new data
        setTimeout(() => {
            this.createVolumeChart();
            this.createStrengthChart();
        }, 100);
    }

    loadRecommendations() {
        const recommendations = StorageManager.generateRecommendations();
        const recommendationsList = document.getElementById('recommendations-list');
        
        if (!recommendationsList) return;

        recommendationsList.innerHTML = '';

        if (recommendations.length === 0) {
            recommendationsList.innerHTML = '<p style="text-align: center; color: #666; padding: 1rem;">No recommendations at this time. Keep up the great work!</p>';
            return;
        }

        recommendations.forEach(recommendation => {
            const recommendationItem = document.createElement('div');
            recommendationItem.className = 'recommendation-item';
            recommendationItem.innerHTML = `
                <i class="${recommendation.icon}"></i>
                <div class="recommendation-text">
                    <h4>${recommendation.title}</h4>
                    <p>${recommendation.message}</p>
                </div>
            `;
            recommendationsList.appendChild(recommendationItem);
        });
    }

    // Advanced Analytics Methods
    calculatePersonalRecords() {
        const sessions = StorageManager.getWorkoutSessions();
        const personalRecords = new Map();

        sessions.forEach(session => {
            session.exercises.forEach(exercise => {
                exercise.sets.forEach(set => {
                    if (set && set.weight && set.reps) {
                        const weight = parseFloat(set.weight);
                        const reps = parseInt(set.reps);
                        
                        if (weight > 0 && reps > 0) {
                            const exerciseName = exercise.name;
                            const oneRepMax = this.calculateOneRepMax(weight, reps);
                            
                            if (!personalRecords.has(exerciseName) || 
                                personalRecords.get(exerciseName).oneRepMax < oneRepMax) {
                                personalRecords.set(exerciseName, {
                                    weight: weight,
                                    reps: reps,
                                    oneRepMax: oneRepMax,
                                    date: session.date,
                                    sessionId: session.id
                                });
                            }
                        }
                    }
                });
            });
        });

        return Object.fromEntries(personalRecords);
    }

    calculateOneRepMax(weight, reps) {
        // Using Brzycki formula: 1RM = weight / (1.0278 - 0.0278 × reps)
        if (reps === 1) return weight;
        return weight / (1.0278 - 0.0278 * reps);
    }

    calculateVolumeLoad(sessions) {
        let totalVolume = 0;
        let totalSets = 0;
        let totalReps = 0;

        sessions.forEach(session => {
            session.exercises.forEach(exercise => {
                exercise.sets.forEach(set => {
                    if (set && set.weight && set.reps) {
                        const weight = parseFloat(set.weight);
                        const reps = parseInt(set.reps);
                        totalVolume += weight * reps;
                        totalSets++;
                        totalReps += reps;
                    }
                });
            });
        });

        return { totalVolume, totalSets, totalReps };
    }

    calculateWorkoutFrequency(period = 'week') {
        const now = new Date();
        let startDate;

        switch (period) {
            case 'week':
                startDate = new Date(now);
                startDate.setDate(now.getDate() - 7);
                break;
            case 'month':
                startDate = new Date(now);
                startDate.setMonth(now.getMonth() - 1);
                break;
            default:
                startDate = new Date(now);
                startDate.setDate(now.getDate() - 7);
        }

        const sessions = StorageManager.getWorkoutSessionsByDateRange(startDate, now);
        const uniqueDays = new Set(sessions.map(session => 
            new Date(session.date).toDateString()
        ));

        return {
            totalWorkouts: sessions.length,
            uniqueDays: uniqueDays.size,
            averagePerWeek: period === 'month' ? (uniqueDays.size / 4) : uniqueDays.size
        };
    }

    generateProgressReport(period = 'month') {
        const now = new Date();
        let startDate;

        switch (period) {
            case 'week':
                startDate = new Date(now);
                startDate.setDate(now.getDate() - 7);
                break;
            case 'month':
                startDate = new Date(now);
                startDate.setMonth(now.getMonth() - 1);
                break;
            case 'year':
                startDate = new Date(now);
                startDate.setFullYear(now.getFullYear() - 1);
                break;
            default:
                startDate = new Date(now);
                startDate.setMonth(now.getMonth() - 1);
        }

        const sessions = StorageManager.getWorkoutSessionsByDateRange(startDate, now);
        const volumeLoad = this.calculateVolumeLoad(sessions);
        const frequency = this.calculateWorkoutFrequency(period);
        const personalRecords = this.calculatePersonalRecords();

        return {
            period: period,
            dateRange: {
                start: startDate.toISOString(),
                end: now.toISOString()
            },
            summary: {
                totalWorkouts: sessions.length,
                totalVolume: volumeLoad.totalVolume,
                totalSets: volumeLoad.totalSets,
                totalReps: volumeLoad.totalReps,
                averageWorkoutDuration: sessions.length > 0 ? 
                    sessions.reduce((sum, s) => sum + (s.duration || 0), 0) / sessions.length : 0
            },
            frequency: frequency,
            personalRecords: personalRecords,
            recommendations: StorageManager.generateRecommendations()
        };
    }

    exportProgressReport(period = 'month') {
        const report = this.generateProgressReport(period);
        const reportText = this.formatProgressReportText(report);
        
        // Create and download file
        const blob = new Blob([reportText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `workout-progress-report-${period}-${new Date().toISOString().split('T')[0]}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    formatProgressReportText(report) {
        const startDate = new Date(report.dateRange.start).toLocaleDateString();
        const endDate = new Date(report.dateRange.end).toLocaleDateString();
        
        let text = `WORKOUT PROGRESS REPORT\n`;
        text += `Period: ${report.period.toUpperCase()}\n`;
        text += `Date Range: ${startDate} - ${endDate}\n`;
        text += `Generated: ${new Date().toLocaleString()}\n\n`;
        
        text += `SUMMARY\n`;
        text += `-------\n`;
        text += `Total Workouts: ${report.summary.totalWorkouts}\n`;
        text += `Total Volume: ${Math.round(report.summary.totalVolume)} kg\n`;
        text += `Total Sets: ${report.summary.totalSets}\n`;
        text += `Total Reps: ${report.summary.totalReps}\n`;
        text += `Average Workout Duration: ${Math.round(report.summary.averageWorkoutDuration / 60000)} minutes\n\n`;
        
        text += `FREQUENCY\n`;
        text += `---------\n`;
        text += `Workout Days: ${report.frequency.uniqueDays}\n`;
        text += `Average Per Week: ${Math.round(report.frequency.averagePerWeek * 10) / 10}\n\n`;
        
        if (Object.keys(report.personalRecords).length > 0) {
            text += `PERSONAL RECORDS\n`;
            text += `----------------\n`;
            Object.entries(report.personalRecords).forEach(([exercise, record]) => {
                text += `${exercise}: ${record.weight}kg x ${record.reps} reps (Est. 1RM: ${Math.round(record.oneRepMax)}kg)\n`;
            });
            text += `\n`;
        }
        
        if (report.recommendations.length > 0) {
            text += `RECOMMENDATIONS\n`;
            text += `---------------\n`;
            report.recommendations.forEach(rec => {
                text += `• ${rec.title}: ${rec.message}\n`;
            });
        }
        
        return text;
    }
}

// Create global progress manager instance
window.progressManager = new ProgressManager();
