/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: #667eea;
}

.nav-menu {
    display: flex;
    gap: 1rem;
}

.nav-btn {
    background: none;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.nav-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.nav-btn.active {
    background: #667eea;
    color: white;
}

/* Main Content */
.main-content {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Dashboard */
.dashboard-header {
    text-align: center;
    margin-bottom: 2rem;
}

.dashboard-header h2 {
    font-size: 2.5rem;
    color: white;
    margin-bottom: 0.5rem;
}

.date {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stat-card i {
    font-size: 2rem;
    color: #667eea;
}

.stat-info h3 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: #666;
    font-size: 0.9rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

.btn-secondary:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

/* Recent Workouts */
.recent-workouts {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.recent-workouts h3 {
    margin-bottom: 1rem;
    color: #333;
}

.workout-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.workout-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.workout-info h4 {
    color: #333;
    margin-bottom: 0.25rem;
}

.workout-info p {
    color: #666;
    font-size: 0.9rem;
}

.workout-stats {
    text-align: right;
    color: #667eea;
    font-weight: 500;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Workouts Grid */
.workouts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.workouts-header h2 {
    color: white;
    font-size: 2rem;
}

.workouts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.workout-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.workout-card:hover {
    transform: translateY(-5px);
}

.workout-card h3 {
    color: #333;
    margin-bottom: 0.5rem;
}

.workout-card p {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.workout-card .exercise-count {
    color: #667eea;
    font-weight: 500;
    margin-bottom: 1rem;
}

.workout-actions {
    display: flex;
    gap: 0.5rem;
}

/* Active Workout */
.workout-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.workout-header h2 {
    color: #333;
}

.workout-timer {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.exercise-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.exercise-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.exercise-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.exercise-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
}

.exercise-timer {
    font-size: 1.1rem;
    color: #667eea;
    font-weight: 500;
}

.sets-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.set-row {
    display: grid;
    grid-template-columns: auto 1fr 1fr 1fr auto;
    gap: 1rem;
    align-items: center;
    padding: 0.75rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
}

.tut-container {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.tut-input {
    flex: 1;
}

.btn-tut {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.btn-tut:hover {
    background: #5a6fd8;
}

.btn-tut.active {
    background: #dc3545;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.set-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.btn-rest {
    background: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.btn-rest:hover {
    background: #218838;
}

/* Time-based Exercise Styling */
.set-row.time-based {
    background: rgba(255, 193, 7, 0.1);
    border-left: 4px solid #ffc107;
}

.time-container {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.time-input {
    flex: 1;
}

.btn-time-start {
    background: #ffc107;
    color: #212529;
    border: none;
    border-radius: 4px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    font-weight: 600;
}

.btn-time-start:hover {
    background: #e0a800;
}

.btn-time-start.active {
    background: #dc3545;
    color: white;
    animation: pulse 1s infinite;
}

/* Two-Set Method Styling */
.set-row.two-set-method {
    background: rgba(118, 75, 162, 0.1);
    border-left: 4px solid #764ba2;
}

.set-row.two-set-method .set-number {
    font-weight: 600;
    color: #764ba2;
}

.two-set-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(118, 75, 162, 0.1);
    border-radius: 8px;
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #764ba2;
}

.two-set-info i {
    color: #764ba2;
}

/* Merged Exercise Styling */
.exercise-card.merged {
    border-left: 4px solid #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.exercise-card.merged::after {
    content: "Merged with next exercise - no rest between";
    display: block;
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    padding: 0.5rem;
    margin-top: 1rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
}

/* Exercise Type Badges */
.exercise-type-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-left: 0.5rem;
    text-transform: uppercase;
}

.exercise-type-badge {
    background: #ffc107;
    color: #212529;
}

.exercise-type-badge.two-set {
    background: #764ba2;
    color: white;
}

/* Break Card Styling */
.break-card {
    background: rgba(255, 193, 7, 0.1);
    border: 2px solid #ffc107;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.break-card .break-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #856404;
    font-weight: 600;
    font-size: 1.1rem;
}

.break-card .break-header i {
    color: #ffc107;
    font-size: 1.2rem;
}

.break-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.break-note {
    color: #856404;
    font-style: italic;
    margin: 0;
    padding: 0.5rem 1rem;
    background: rgba(255, 193, 7, 0.2);
    border-radius: 8px;
    border-left: 4px solid #ffc107;
}

.set-number {
    font-weight: 600;
    color: #667eea;
}

.set-input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    text-align: center;
    font-size: 0.9rem;
}

.set-input:focus {
    outline: none;
    border-color: #667eea;
}

.set-complete {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.workout-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 700px;
    max-height: 95vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    color: #333;
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.modal-body {
    padding: 1.5rem;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.modal-footer {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid #eee;
}

/* Form Styles */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.exercise-input {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 0.75rem;
    align-items: end;
    margin-bottom: 0.75rem;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
}

.remove-exercise {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.5rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.remove-exercise:hover {
    background: #c82333;
}

/* Progress Styles */
.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.progress-header h2 {
    color: white;
    font-size: 2rem;
}

.time-filter {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.filter-btn.active {
    background: white;
    color: #667eea;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.progress-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.progress-card h3 {
    color: #333;
    margin-bottom: 1rem;
    text-align: center;
}

.recommendations {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.recommendations h3 {
    color: #333;
    margin-bottom: 1rem;
}

.recommendation-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    margin-bottom: 0.75rem;
    border-left: 4px solid #667eea;
}

.recommendation-item i {
    color: #667eea;
    font-size: 1.2rem;
}

.recommendation-text {
    flex: 1;
}

.recommendation-text h4 {
    color: #333;
    margin-bottom: 0.25rem;
}

.recommendation-text p {
    color: #666;
    font-size: 0.9rem;
}

/* Enhanced Exercise Input */
.exercise-input {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.exercise-input.merged {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.exercise-input.merged::after {
    content: "Merged with next exercise";
    display: block;
    color: #28a745;
    font-size: 0.8rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

.exercise-header {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
    position: relative;
}

.exercise-header .form-group {
    flex: 0 1 300px;
    margin-bottom: 0;
    text-align: center;
}

.exercise-controls {
    display: flex;
    gap: 0.5rem;
    position: absolute;
    right: 0;
    top: 0;
}

.btn-merge {
    background: #28a745;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-merge:hover {
    background: #218838;
    transform: translateY(-1px);
}

.exercise-type-toggle {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background: rgba(102, 126, 234, 0.08);
    border-radius: 8px;
    justify-content: center;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.toggle-label:hover {
    background: rgba(102, 126, 234, 0.2);
}

.toggle-label input[type="radio"] {
    margin: 0;
}

.toggle-label input[type="radio"]:checked + span {
    color: #667eea;
    font-weight: 600;
}

.exercise-params {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.two-set-method {
    margin-bottom: 0;
    padding: 0.75rem;
    background: rgba(118, 75, 162, 0.08);
    border-radius: 8px;
    border-left: 4px solid #764ba2;
    border: 1px solid rgba(118, 75, 162, 0.2);
}

.checkbox-label {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 0.5rem;
}

.checkbox-label small {
    color: #666;
    font-style: italic;
    margin-left: 1.5rem;
}



.rest-settings {
    padding: 0.75rem;
    background: rgba(40, 167, 69, 0.08);
    border-radius: 8px;
    border-left: 4px solid #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
    margin-bottom: 0.75rem;
}

/* Break Input Styling */
.break-input {
    background: rgba(255, 193, 7, 0.1);
    border: 2px solid #ffc107;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.break-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #856404;
    font-weight: 600;
}

.break-header i {
    color: #ffc107;
}

.remove-break {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    margin-left: auto;
}

.remove-break:hover {
    background: #c82333;
}

/* Exercise Search and Suggestions */
.exercise-search-container {
    position: relative;
    margin-bottom: 1rem;
}

.exercise-search-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
}

.exercise-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.exercise-suggestion {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    transition: background 0.2s ease;
}

.exercise-suggestion:hover {
    background: rgba(102, 126, 234, 0.1);
}

.exercise-suggestion:last-child {
    border-bottom: none;
}

.exercise-suggestion-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 0.25rem;
}

.exercise-suggestion-details {
    font-size: 0.8rem;
    color: #666;
}

/* Enhanced Workout Cards */
.workout-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.workout-card-menu {
    position: relative;
}

.menu-btn {
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    color: #666;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.menu-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 150px;
    z-index: 100;
    display: none;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu button {
    width: 100%;
    padding: 0.75rem;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    transition: background 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dropdown-menu button:hover {
    background: rgba(102, 126, 234, 0.1);
}

.workout-description {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    line-height: 1.4;
}

.workout-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
}

.stat {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #667eea;
}

.stat-label {
    font-size: 0.8rem;
    color: #666;
}

.workout-exercises {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.exercise-tag {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.exercise-tag.more {
    background: rgba(118, 75, 162, 0.1);
    color: #764ba2;
}

.mini-badge {
    display: inline-block;
    padding: 0.1rem 0.3rem;
    border-radius: 8px;
    font-size: 0.6rem;
    font-weight: 600;
    margin-left: 0.25rem;
    text-transform: uppercase;
    background: rgba(102, 126, 234, 0.2);
    color: #667eea;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

.btn-warning {
    background: #ffc107;
    color: #212529;
    font-weight: 600;
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
}

.workout-builder-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: white;
    grid-column: 1 / -1;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 2rem;
    opacity: 0.8;
}

/* Rest Timer Modal */
.rest-timer-modal .modal-content {
    max-width: 400px;
    text-align: center;
}

.rest-timer-header {
    text-align: center;
    position: relative;
}

.rest-timer-header .close-btn {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

.rest-timer-circle {
    width: 200px;
    height: 200px;
    border: 8px solid #eee;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 2rem auto;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.rest-timer-display {
    font-size: 2.5rem;
    font-weight: bold;
    color: white;
}

.rest-progress-container {
    width: 100%;
    height: 8px;
    background: #eee;
    border-radius: 4px;
    margin-bottom: 2rem;
    overflow: hidden;
}

.rest-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 1s linear;
}

.rest-timer-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    max-width: 300px;
    z-index: 2000;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.notification.fade-out {
    animation: slideOutRight 0.3s ease;
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

.notification-success {
    border-left: 4px solid #28a745;
}

.notification-error {
    border-left: 4px solid #dc3545;
}

.notification-info {
    border-left: 4px solid #667eea;
}

.notification-content {
    flex: 1;
}

.notification-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    color: #333;
}

.notification-content p {
    margin: 0;
    font-size: 0.8rem;
    color: #666;
}

.notification-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 0.25rem;
}

.notification-close:hover {
    color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }

    .nav-menu {
        gap: 0.5rem;
    }

    .nav-btn span {
        display: none;
    }

    .main-content {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        flex-direction: column;
    }

    .workouts-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .workout-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .set-row {
        grid-template-columns: auto 1fr 1fr auto;
        gap: 0.5rem;
    }

    .tut-container {
        flex-direction: column;
        gap: 0.25rem;
    }

    .set-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .exercise-input {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        padding: 1.5rem;
    }

    .exercise-params {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .exercise-type-toggle {
        gap: 1rem;
        padding: 0.75rem;
    }

    .progress-stats {
        grid-template-columns: 1fr;
    }

    .progress-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-footer {
        flex-direction: column;
    }
}
