// Main Application Controller
class WorkoutTracker {
    constructor() {
        this.currentTab = 'dashboard';
        this.activeWorkout = null;
        this.workoutTimer = null;
        this.exerciseTimers = new Map();
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateDashboard();
        this.loadWorkouts();
        this.updateCurrentDate();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.closest('.nav-btn').dataset.tab);
            });
        });

        // Dashboard actions
        document.getElementById('start-quick-workout')?.addEventListener('click', () => {
            this.startQuickWorkout();
        });

        document.getElementById('create-new-workout')?.addEventListener('click', () => {
            this.openWorkoutModal();
        });

        // Workout management
        document.getElementById('add-workout-btn')?.addEventListener('click', () => {
            this.openWorkoutModal();
        });

        // Modal controls
        document.getElementById('close-modal')?.addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('cancel-workout')?.addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('save-workout')?.addEventListener('click', () => {
            window.workoutManager.saveWorkout();
        });

        document.getElementById('add-exercise')?.addEventListener('click', () => {
            window.workoutManager.addExerciseInput();
        });

        // Active workout controls
        document.getElementById('pause-workout')?.addEventListener('click', () => {
            this.pauseWorkout();
        });

        document.getElementById('finish-workout')?.addEventListener('click', () => {
            this.finishWorkout();
        });

        // Progress filters
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.updateProgressFilter(e.target.dataset.period);
            });
        });

        // Close modal when clicking outside
        document.getElementById('workout-modal')?.addEventListener('click', (e) => {
            if (e.target.id === 'workout-modal') {
                this.closeModal();
            }
        });
    }

    switchTab(tabName) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;

        // Load tab-specific content
        switch(tabName) {
            case 'dashboard':
                this.updateDashboard();
                break;
            case 'workouts':
                this.loadWorkouts();
                break;
            case 'active-workout':
                this.loadActiveWorkout();
                break;
            case 'progress':
                this.loadProgress();
                break;
        }
    }

    updateCurrentDate() {
        const now = new Date();
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        document.getElementById('current-date').textContent = now.toLocaleDateString('en-US', options);
    }

    updateDashboard() {
        const stats = StorageManager.getWeeklyStats();
        const recentWorkouts = StorageManager.getRecentWorkouts(5);

        // Update stats
        document.getElementById('workouts-this-week').textContent = stats.workoutsThisWeek;
        document.getElementById('total-time').textContent = `${Math.round(stats.totalTime / 60)}m`;
        document.getElementById('streak-count').textContent = stats.streak;

        // Update recent workouts
        const recentWorkoutsList = document.getElementById('recent-workouts-list');
        recentWorkoutsList.innerHTML = '';

        if (recentWorkouts.length === 0) {
            recentWorkoutsList.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">No recent workouts. Start your first workout!</p>';
            return;
        }

        recentWorkouts.forEach(workout => {
            const workoutItem = document.createElement('div');
            workoutItem.className = 'workout-item';
            workoutItem.innerHTML = `
                <div class="workout-info">
                    <h4>${workout.name}</h4>
                    <p>${new Date(workout.date).toLocaleDateString()} • ${workout.exercises.length} exercises</p>
                </div>
                <div class="workout-stats">
                    <div>${Math.round(workout.duration / 60)}m</div>
                </div>
            `;
            recentWorkoutsList.appendChild(workoutItem);
        });
    }

    loadWorkouts() {
        const workouts = StorageManager.getWorkouts();
        const workoutsList = document.getElementById('workouts-list');
        workoutsList.innerHTML = '';

        if (workouts.length === 0) {
            workoutsList.innerHTML = '<p style="text-align: center; color: white; padding: 2rem; grid-column: 1 / -1;">No workouts created yet. Create your first workout!</p>';
            return;
        }

        workouts.forEach(workout => {
            const workoutCard = document.createElement('div');
            workoutCard.className = 'workout-card';
            workoutCard.innerHTML = `
                <h3>${workout.name}</h3>
                <p>${workout.description || 'No description'}</p>
                <div class="exercise-count">${workout.exercises.length} exercises</div>
                <div class="workout-actions">
                    <button class="btn btn-primary" onclick="app.startWorkout('${workout.id}')">
                        <i class="fas fa-play"></i>
                        Start
                    </button>
                    <button class="btn btn-secondary" onclick="app.editWorkout('${workout.id}')">
                        <i class="fas fa-edit"></i>
                        Edit
                    </button>
                    <button class="btn btn-secondary" onclick="app.deleteWorkout('${workout.id}')">
                        <i class="fas fa-trash"></i>
                        Delete
                    </button>
                </div>
            `;
            workoutsList.appendChild(workoutCard);
        });
    }

    startQuickWorkout() {
        // Create a quick workout with common exercises
        const quickWorkout = {
            id: 'quick-' + Date.now(),
            name: 'Quick Workout',
            description: 'A quick workout session',
            exercises: [
                { name: 'Push-ups', sets: 3, reps: 10, weight: 0 },
                { name: 'Squats', sets: 3, reps: 15, weight: 0 },
                { name: 'Plank', sets: 3, reps: 1, weight: 0, duration: 30 }
            ]
        };
        
        this.startWorkout(null, quickWorkout);
    }

    startWorkout(workoutId, quickWorkout = null) {
        let workout;

        if (quickWorkout) {
            workout = quickWorkout;
        } else {
            const workouts = StorageManager.getWorkouts();
            workout = workouts.find(w => w.id === workoutId);
        }

        if (!workout) {
            alert('Workout not found!');
            return;
        }

        // Convert old format to new format if needed
        if (workout.exercises && !workout.items) {
            workout.items = workout.exercises.map(exercise => ({
                type: 'exercise',
                ...exercise,
                exerciseType: exercise.exerciseType || 'reps',
                restTime: exercise.restTime || 90,
                twoSetMethod: exercise.twoSetMethod || false,
                isMerged: exercise.isMerged || false
            }));
        }

        // Ensure backward compatibility
        if (workout.items) {
            workout.exercises = workout.items.filter(item => item.type === 'exercise');
        }

        this.activeWorkout = {
            ...workout,
            startTime: Date.now(),
            completedSets: new Map(),
            exerciseStartTimes: new Map(),
            currentItemIndex: 0,
            isInBreak: false
        };

        this.switchTab('active-workout');
        this.loadActiveWorkout();
        this.startWorkoutTimer();
    }

    loadActiveWorkout() {
        if (!this.activeWorkout) {
            document.getElementById('active-workout-name').textContent = 'Select a Workout';
            document.getElementById('exercise-container').innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">No active workout. Go to Workouts tab to start one.</p>';
            return;
        }

        document.getElementById('active-workout-name').textContent = this.activeWorkout.name;

        const exerciseContainer = document.getElementById('exercise-container');
        exerciseContainer.innerHTML = '';

        if (this.activeWorkout.items) {
            // New format with items (exercises and breaks)
            let exerciseIndex = 0;
            this.activeWorkout.items.forEach((item, itemIndex) => {
                if (item.type === 'exercise') {
                    const exerciseCard = document.createElement('div');
                    exerciseCard.className = `exercise-card ${item.isMerged ? 'merged' : ''}`;
                    exerciseCard.innerHTML = `
                        <div class="exercise-header">
                            <div class="exercise-name">
                                ${item.name}
                                ${item.exerciseType === 'time' ? '<span class="exercise-type-badge">TIME</span>' : ''}
                                ${item.twoSetMethod ? '<span class="exercise-type-badge two-set">2-SET</span>' : ''}
                            </div>
                            <div class="exercise-timer" id="exercise-timer-${exerciseIndex}">00:00</div>
                        </div>
                        <div class="sets-container" id="sets-${exerciseIndex}">
                            ${this.generateSetsHTML(item, exerciseIndex)}
                        </div>
                    `;
                    exerciseContainer.appendChild(exerciseCard);
                    exerciseIndex++;
                } else if (item.type === 'break') {
                    const breakCard = document.createElement('div');
                    breakCard.className = 'break-card';
                    breakCard.innerHTML = `
                        <div class="break-header">
                            <i class="fas fa-clock"></i>
                            <span>Rest Break - ${item.duration}s</span>
                        </div>
                        <div class="break-content">
                            ${item.note ? `<p class="break-note">${item.note}</p>` : ''}
                            <button class="btn btn-warning" onclick="window.timerManager?.startRestTimer(${item.duration})">
                                <i class="fas fa-play"></i>
                                Start ${item.duration}s Break
                            </button>
                        </div>
                    `;
                    exerciseContainer.appendChild(breakCard);
                }
            });
        } else {
            // Backward compatibility with old format
            this.activeWorkout.exercises.forEach((exercise, exerciseIndex) => {
                const exerciseCard = document.createElement('div');
                exerciseCard.className = 'exercise-card';
                exerciseCard.innerHTML = `
                    <div class="exercise-header">
                        <div class="exercise-name">${exercise.name}</div>
                        <div class="exercise-timer" id="exercise-timer-${exerciseIndex}">00:00</div>
                    </div>
                    <div class="sets-container" id="sets-${exerciseIndex}">
                        ${this.generateSetsHTML(exercise, exerciseIndex)}
                    </div>
                `;
                exerciseContainer.appendChild(exerciseCard);
            });
        }
    }

    generateSetsHTML(exercise, exerciseIndex) {
        let html = '';
        const isTimeBased = exercise.exerciseType === 'time';
        const isTwoSetMethod = exercise.twoSetMethod;
        const actualSets = isTwoSetMethod ? 2 : exercise.sets;

        for (let setIndex = 0; setIndex < actualSets; setIndex++) {
            const setKey = `${exerciseIndex}-${setIndex}`;
            const completed = this.activeWorkout.completedSets.has(setKey);

            let setLabel = `Set ${setIndex + 1}`;
            if (isTwoSetMethod) {
                setLabel = setIndex === 0 ? 'Set 1 (To Pressure)' : 'Set 2 (To Failure)';
            }

            html += `
                <div class="set-row ${isTwoSetMethod ? 'two-set-method' : ''} ${isTimeBased ? 'time-based' : ''}">
                    <div class="set-number">${setLabel}</div>
                    <input type="number" class="set-input" placeholder="Weight (kg)"
                           id="weight-${setKey}" value="${exercise.weight || ''}" min="0" step="0.5">
            `;

            if (isTimeBased) {
                html += `
                    <div class="time-container">
                        <input type="number" class="set-input time-input" placeholder="Duration (s)"
                               id="duration-${setKey}" value="${exercise.duration || ''}" min="1" readonly>
                        <button type="button" class="btn-time-start" id="time-start-${setKey}"
                                onclick="window.timerManager?.startExerciseTimer('${setKey}', ${exercise.duration || 60})">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                `;
            } else {
                html += `
                    <input type="number" class="set-input" placeholder="Reps"
                           id="reps-${setKey}" value="${exercise.reps || ''}" min="1">
                `;
            }

            html += `
                    <div class="tut-container">
                        <input type="number" class="set-input tut-input" placeholder="TUT (s)"
                               id="tut-${setKey}" value="" min="1" readonly>
                        <button type="button" class="btn-tut" id="tut-start-${setKey}"
                                onclick="window.timerManager?.startTUTTimer('${setKey}')">
                            <i class="fas fa-stopwatch"></i>
                        </button>
                    </div>
                    <div class="set-actions">
                        <input type="checkbox" class="set-complete"
                               id="complete-${setKey}" ${completed ? 'checked' : ''}
                               onchange="app.toggleSetComplete('${setKey}')">
                        <button type="button" class="btn-rest"
                                onclick="window.timerManager?.startRestTimer(${exercise.restTime || 90})"
                                title="Start Rest Timer (${exercise.restTime || 90}s)">
                            <i class="fas fa-clock"></i>
                        </button>
                    </div>
                </div>
            `;
        }

        if (isTwoSetMethod) {
            html += `
                <div class="two-set-info">
                    <i class="fas fa-info-circle"></i>
                    <span>Two-Set Method: First set to muscle pressure, second set to failure</span>
                </div>
            `;
        }

        return html;
    }

    toggleSetComplete(setKey) {
        const checkbox = document.getElementById(`complete-${setKey}`);
        if (checkbox.checked) {
            this.activeWorkout.completedSets.set(setKey, {
                weight: document.getElementById(`weight-${setKey}`).value,
                reps: document.getElementById(`reps-${setKey}`).value,
                tut: document.getElementById(`tut-${setKey}`).value,
                timestamp: Date.now()
            });
        } else {
            this.activeWorkout.completedSets.delete(setKey);
        }
    }

    startWorkoutTimer() {
        if (window.timerManager) {
            window.timerManager.startWorkoutTimer();
        } else {
            // Fallback timer
            this.workoutTimer = setInterval(() => {
                const elapsed = Date.now() - this.activeWorkout.startTime;
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                document.getElementById('workout-time').textContent =
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }
    }

    pauseWorkout() {
        if (window.timerManager) {
            if (window.timerManager.isWorkoutActive) {
                window.timerManager.pauseWorkoutTimer();
                document.getElementById('pause-workout').innerHTML = '<i class="fas fa-play"></i> Resume';
            } else {
                window.timerManager.resumeWorkoutTimer();
                document.getElementById('pause-workout').innerHTML = '<i class="fas fa-pause"></i> Pause';
            }
        } else {
            // Fallback pause functionality
            if (this.workoutTimer) {
                clearInterval(this.workoutTimer);
                this.workoutTimer = null;
                document.getElementById('pause-workout').innerHTML = '<i class="fas fa-play"></i> Resume';
            } else {
                this.startWorkoutTimer();
                document.getElementById('pause-workout').innerHTML = '<i class="fas fa-pause"></i> Pause';
            }
        }
    }

    finishWorkout() {
        if (!this.activeWorkout) return;

        const duration = window.timerManager ?
            window.timerManager.getWorkoutDuration() :
            Date.now() - this.activeWorkout.startTime;

        const workoutData = {
            id: Date.now().toString(),
            name: this.activeWorkout.name,
            date: new Date().toISOString(),
            duration: duration,
            exercises: this.activeWorkout.exercises.map((exercise, exerciseIndex) => ({
                ...exercise,
                sets: Array.from({length: exercise.sets}, (_, setIndex) => {
                    const setKey = `${exerciseIndex}-${setIndex}`;
                    return this.activeWorkout.completedSets.get(setKey) || null;
                }).filter(set => set !== null)
            }))
        };

        StorageManager.saveWorkoutSession(workoutData);

        // Stop all timers
        if (window.timerManager) {
            window.timerManager.stopWorkoutTimer();
        } else if (this.workoutTimer) {
            clearInterval(this.workoutTimer);
        }

        this.activeWorkout = null;

        if (window.timerManager) {
            window.timerManager.showInAppNotification(
                'Workout Complete!',
                'Great job! Your workout has been saved.',
                'success'
            );
        } else {
            alert('Workout completed! Great job!');
        }

        this.switchTab('dashboard');
    }

    // Modal and workout management methods will be added in the next part
    openWorkoutModal() {
        document.getElementById('workout-modal').classList.add('active');
        document.getElementById('workout-name').value = '';
        document.getElementById('workout-description').value = '';
        document.getElementById('exercises-list').innerHTML = '';
        window.workoutManager.addExerciseInput();
    }

    closeModal() {
        document.getElementById('workout-modal').classList.remove('active');
    }

    addExerciseInput() {
        const exercisesList = document.getElementById('exercises-list');
        const exerciseDiv = document.createElement('div');
        exerciseDiv.className = 'exercise-input';
        exerciseDiv.innerHTML = `
            <div class="form-group">
                <input type="text" placeholder="Exercise name" class="exercise-name-input" required>
            </div>
            <div class="form-group">
                <input type="number" placeholder="Sets" class="exercise-sets-input" min="1" value="3" required>
            </div>
            <div class="form-group">
                <input type="number" placeholder="Reps" class="exercise-reps-input" min="1" value="10" required>
            </div>
            <button type="button" class="remove-exercise" onclick="this.parentElement.remove()">
                <i class="fas fa-trash"></i>
            </button>
        `;
        exercisesList.appendChild(exerciseDiv);
    }

    saveWorkout() {
        const name = document.getElementById('workout-name').value.trim();
        const description = document.getElementById('workout-description').value.trim();
        
        if (!name) {
            alert('Please enter a workout name');
            return;
        }

        const exerciseInputs = document.querySelectorAll('.exercise-input');
        const exercises = [];

        exerciseInputs.forEach(input => {
            const exerciseName = input.querySelector('.exercise-name-input').value.trim();
            const sets = parseInt(input.querySelector('.exercise-sets-input').value);
            const reps = parseInt(input.querySelector('.exercise-reps-input').value);

            if (exerciseName && sets && reps) {
                exercises.push({
                    name: exerciseName,
                    sets: sets,
                    reps: reps,
                    weight: 0
                });
            }
        });

        if (exercises.length === 0) {
            alert('Please add at least one exercise');
            return;
        }

        const workout = {
            id: Date.now().toString(),
            name: name,
            description: description,
            exercises: exercises,
            created: new Date().toISOString()
        };

        StorageManager.saveWorkout(workout);
        this.closeModal();
        this.loadWorkouts();
        alert('Workout saved successfully!');
    }

    editWorkout(workoutId) {
        // Implementation for editing workouts
        alert('Edit functionality coming soon!');
    }

    deleteWorkout(workoutId) {
        if (confirm('Are you sure you want to delete this workout?')) {
            StorageManager.deleteWorkout(workoutId);
            this.loadWorkouts();
        }
    }

    loadProgress() {
        // Progress loading will be implemented in progress.js
        console.log('Loading progress data...');
    }

    updateProgressFilter(period) {
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-period="${period}"]`).classList.add('active');
        
        // Update charts based on period
        if (window.progressManager) {
            window.progressManager.updateCharts(period);
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new WorkoutTracker();
});
