// Timer Management for Workouts and Exercises
class TimerManager {
    constructor() {
        this.workoutTimer = null;
        this.exerciseTimers = new Map();
        this.restTimer = null;
        this.isWorkoutActive = false;
        this.workoutStartTime = null;
        this.exerciseStartTimes = new Map();
        this.restStartTime = null;
        this.restDuration = 60; // Default rest time in seconds
        
        this.setupRestTimerModal();
    }

    // Workout Timer Methods
    startWorkoutTimer() {
        if (this.isWorkoutActive) return;
        
        this.isWorkoutActive = true;
        this.workoutStartTime = Date.now();
        
        this.workoutTimer = setInterval(() => {
            this.updateWorkoutDisplay();
        }, 1000);
    }

    pauseWorkoutTimer() {
        if (this.workoutTimer) {
            clearInterval(this.workoutTimer);
            this.workoutTimer = null;
            this.isWorkoutActive = false;
        }
    }

    resumeWorkoutTimer() {
        if (!this.workoutTimer && this.workoutStartTime) {
            this.isWorkoutActive = true;
            this.workoutTimer = setInterval(() => {
                this.updateWorkoutDisplay();
            }, 1000);
        }
    }

    stopWorkoutTimer() {
        if (this.workoutTimer) {
            clearInterval(this.workoutTimer);
            this.workoutTimer = null;
        }
        this.isWorkoutActive = false;
        this.workoutStartTime = null;
        
        // Clear all exercise timers
        this.exerciseTimers.forEach((timer, exerciseId) => {
            clearInterval(timer);
        });
        this.exerciseTimers.clear();
        this.exerciseStartTimes.clear();
    }

    updateWorkoutDisplay() {
        if (!this.workoutStartTime) return;
        
        const elapsed = Date.now() - this.workoutStartTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);
        
        const workoutTimeElement = document.getElementById('workout-time');
        if (workoutTimeElement) {
            workoutTimeElement.textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    getWorkoutDuration() {
        if (!this.workoutStartTime) return 0;
        return Date.now() - this.workoutStartTime;
    }

    // Exercise Timer Methods
    startExerciseTimer(exerciseId) {
        if (this.exerciseTimers.has(exerciseId)) return;
        
        this.exerciseStartTimes.set(exerciseId, Date.now());
        
        const timer = setInterval(() => {
            this.updateExerciseDisplay(exerciseId);
        }, 1000);
        
        this.exerciseTimers.set(exerciseId, timer);
    }

    stopExerciseTimer(exerciseId) {
        const timer = this.exerciseTimers.get(exerciseId);
        if (timer) {
            clearInterval(timer);
            this.exerciseTimers.delete(exerciseId);
        }
        this.exerciseStartTimes.delete(exerciseId);
    }

    updateExerciseDisplay(exerciseId) {
        const startTime = this.exerciseStartTimes.get(exerciseId);
        if (!startTime) return;
        
        const elapsed = Date.now() - startTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);
        
        const exerciseTimeElement = document.getElementById(`exercise-timer-${exerciseId}`);
        if (exerciseTimeElement) {
            exerciseTimeElement.textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    getExerciseDuration(exerciseId) {
        const startTime = this.exerciseStartTimes.get(exerciseId);
        if (!startTime) return 0;
        return Date.now() - startTime;
    }

    // Rest Timer Methods
    startRestTimer(duration = null) {
        if (this.restTimer) {
            clearInterval(this.restTimer);
        }

        this.restDuration = duration || StorageManager.getSettings().restTimer || 60;
        this.restStartTime = Date.now();
        let remainingTime = this.restDuration;

        this.showRestTimerModal(remainingTime);

        this.restTimer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.restStartTime) / 1000);
            remainingTime = this.restDuration - elapsed;

            if (remainingTime <= 0) {
                this.completeRestTimer();
            } else {
                this.updateRestTimerDisplay(remainingTime);
            }
        }, 1000);
    }

    completeRestTimer() {
        if (this.restTimer) {
            clearInterval(this.restTimer);
            this.restTimer = null;
        }

        this.hideRestTimerModal();
        this.playRestCompleteSound();
        this.showRestCompleteNotification();
    }

    stopRestTimer() {
        if (this.restTimer) {
            clearInterval(this.restTimer);
            this.restTimer = null;
        }
        this.hideRestTimerModal();
    }

    updateRestTimerDisplay(remainingSeconds) {
        const minutes = Math.floor(remainingSeconds / 60);
        const seconds = remainingSeconds % 60;
        
        const restTimerElement = document.getElementById('rest-timer-display');
        if (restTimerElement) {
            restTimerElement.textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // Update progress bar
        const progressBar = document.getElementById('rest-progress-bar');
        if (progressBar) {
            const progress = ((this.restDuration - remainingSeconds) / this.restDuration) * 100;
            progressBar.style.width = `${progress}%`;
        }
    }

    // Rest Timer Modal Methods
    setupRestTimerModal() {
        // Create rest timer modal if it doesn't exist
        if (!document.getElementById('rest-timer-modal')) {
            const modal = document.createElement('div');
            modal.id = 'rest-timer-modal';
            modal.className = 'modal rest-timer-modal';
            modal.innerHTML = `
                <div class="modal-content rest-timer-content">
                    <div class="rest-timer-header">
                        <h3>Rest Timer</h3>
                        <button class="close-btn" id="close-rest-timer">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="rest-timer-body">
                        <div class="rest-timer-circle">
                            <div class="rest-timer-display" id="rest-timer-display">01:00</div>
                        </div>
                        <div class="rest-progress-container">
                            <div class="rest-progress-bar" id="rest-progress-bar"></div>
                        </div>
                        <div class="rest-timer-controls">
                            <button class="btn btn-secondary" id="add-30s">+30s</button>
                            <button class="btn btn-primary" id="skip-rest">Skip Rest</button>
                            <button class="btn btn-secondary" id="subtract-30s">-30s</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // Add event listeners
            document.getElementById('close-rest-timer').addEventListener('click', () => {
                this.stopRestTimer();
            });

            document.getElementById('skip-rest').addEventListener('click', () => {
                this.completeRestTimer();
            });

            document.getElementById('add-30s').addEventListener('click', () => {
                this.adjustRestTimer(30);
            });

            document.getElementById('subtract-30s').addEventListener('click', () => {
                this.adjustRestTimer(-30);
            });
        }
    }

    showRestTimerModal(remainingTime) {
        const modal = document.getElementById('rest-timer-modal');
        if (modal) {
            modal.classList.add('active');
            this.updateRestTimerDisplay(remainingTime);
        }
    }

    hideRestTimerModal() {
        const modal = document.getElementById('rest-timer-modal');
        if (modal) {
            modal.classList.remove('active');
        }
    }

    adjustRestTimer(seconds) {
        if (!this.restTimer) return;

        this.restDuration += seconds;
        if (this.restDuration < 0) this.restDuration = 0;
    }

    // Time Under Tension (TUT) Tracking
    startTUTTimer(setKey) {
        const tutStartTime = Date.now();
        
        // Store TUT start time for this set
        if (!window.tutTimers) {
            window.tutTimers = new Map();
        }
        
        window.tutTimers.set(setKey, tutStartTime);
        
        // Visual feedback
        const tutButton = document.getElementById(`tut-start-${setKey}`);
        if (tutButton) {
            tutButton.textContent = 'Stop TUT';
            tutButton.classList.add('active');
            tutButton.onclick = () => this.stopTUTTimer(setKey);
        }
    }

    stopTUTTimer(setKey) {
        if (!window.tutTimers || !window.tutTimers.has(setKey)) return;
        
        const startTime = window.tutTimers.get(setKey);
        const duration = Math.floor((Date.now() - startTime) / 1000);
        
        // Update TUT input field
        const tutInput = document.getElementById(`tut-${setKey}`);
        if (tutInput) {
            tutInput.value = duration;
        }
        
        // Reset button
        const tutButton = document.getElementById(`tut-start-${setKey}`);
        if (tutButton) {
            tutButton.textContent = 'Start TUT';
            tutButton.classList.remove('active');
            tutButton.onclick = () => this.startTUTTimer(setKey);
        }
        
        window.tutTimers.delete(setKey);
    }

    // Sound and Notification Methods
    playRestCompleteSound() {
        const settings = StorageManager.getSettings();
        if (!settings.soundEnabled) return;

        // Create audio context for beep sound
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = 800;
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
        } catch (error) {
            console.log('Audio not supported');
        }
    }

    showRestCompleteNotification() {
        // Show browser notification if supported
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('Rest Complete!', {
                body: 'Time to start your next set.',
                icon: '/favicon.ico'
            });
        }

        // Show in-app notification
        this.showInAppNotification('Rest Complete!', 'Time for your next set', 'success');
    }

    showInAppNotification(title, message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <h4>${title}</h4>
                <p>${message}</p>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);

        // Manual close
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        });
    }

    // Utility Methods
    formatTime(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    formatTimeFromSeconds(totalSeconds) {
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    // Request notification permission
    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }
}

    // Time-based Exercise Timer Methods
    startExerciseTimer(setKey, duration) {
        const button = document.getElementById(`time-start-${setKey}`);
        const input = document.getElementById(`duration-${setKey}`);

        if (!button || !input) return;

        if (!this.timeBasedTimers) {
            this.timeBasedTimers = new Map();
        }

        if (this.timeBasedTimers.has(setKey)) {
            // Stop existing timer
            this.stopTimeBasedTimer(setKey);
            return;
        }

        const startTime = Date.now();
        const endTime = startTime + (duration * 1000);

        button.innerHTML = '<i class="fas fa-stop"></i>';
        button.classList.add('active');
        button.title = 'Stop timer';

        const timer = setInterval(() => {
            const now = Date.now();
            const remaining = Math.max(0, Math.ceil((endTime - now) / 1000));

            input.value = remaining;

            if (remaining <= 0) {
                this.completeTimeBasedTimer(setKey);
            }
        }, 100);

        this.timeBasedTimers.set(setKey, {
            timer: timer,
            startTime: startTime,
            duration: duration,
            endTime: endTime
        });
    }

    stopTimeBasedTimer(setKey) {
        if (!this.timeBasedTimers) return;

        const timerData = this.timeBasedTimers.get(setKey);
        if (!timerData) return;

        clearInterval(timerData.timer);
        this.timeBasedTimers.delete(setKey);

        const button = document.getElementById(`time-start-${setKey}`);
        const input = document.getElementById(`duration-${setKey}`);

        if (button) {
            button.innerHTML = '<i class="fas fa-play"></i>';
            button.classList.remove('active');
            button.title = 'Start timer';
        }

        if (input) {
            const elapsed = Math.ceil((Date.now() - timerData.startTime) / 1000);
            input.value = elapsed;
        }
    }

    completeTimeBasedTimer(setKey) {
        if (!this.timeBasedTimers) return;

        const timerData = this.timeBasedTimers.get(setKey);
        if (!timerData) return;

        this.stopTimeBasedTimer(setKey);

        // Play completion sound
        this.playNotificationSound();

        // Show notification
        this.showInAppNotification(
            'Exercise Timer Complete!',
            `Time-based exercise completed`,
            'success'
        );

        // Auto-check the set as complete
        const checkbox = document.getElementById(`complete-${setKey}`);
        if (checkbox && !checkbox.checked) {
            checkbox.checked = true;
            checkbox.dispatchEvent(new Event('change'));
        }
    }
}

// Create global timer manager instance
window.timerManager = new TimerManager();

// Request notification permission on load
document.addEventListener('DOMContentLoaded', () => {
    window.timerManager.requestNotificationPermission();
});
