// Workout Management and Exercise Database
class WorkoutManager {
    constructor() {
        this.exerciseDatabase = this.initializeExerciseDatabase();
        this.currentEditingWorkout = null;
        this.init();
    }

    init() {
        this.setupWorkoutManagement();
        this.setupExerciseSearch();
    }

    setupWorkoutManagement() {
        // Enhanced workout form functionality
        const workoutForm = document.getElementById('workout-form');
        if (workoutForm) {
            workoutForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveWorkout();
            });
        }
    }

    setupExerciseSearch() {
        // Add exercise search functionality
        const exercisesList = document.getElementById('exercises-list');
        if (exercisesList) {
            // Create search input for exercises
            const searchContainer = document.createElement('div');
            searchContainer.className = 'exercise-search-container';
            searchContainer.innerHTML = `
                <input type="text" id="exercise-search" placeholder="Search exercises..." class="exercise-search-input">
                <div id="exercise-suggestions" class="exercise-suggestions"></div>
            `;
            
            // Insert before the exercises list
            exercisesList.parentNode.insertBefore(searchContainer, exercisesList);
            
            const searchInput = document.getElementById('exercise-search');
            const suggestionsDiv = document.getElementById('exercise-suggestions');
            
            searchInput.addEventListener('input', (e) => {
                this.showExerciseSuggestions(e.target.value, suggestionsDiv);
            });
        }
    }

    initializeExerciseDatabase() {
        return {
            'Chest': [
                { name: 'Bench Press', equipment: 'Barbell', difficulty: 'Intermediate' },
                { name: 'Incline Bench Press', equipment: 'Barbell', difficulty: 'Intermediate' },
                { name: 'Dumbbell Press', equipment: 'Dumbbell', difficulty: 'Beginner' },
                { name: 'Incline Dumbbell Press', equipment: 'Dumbbell', difficulty: 'Beginner' },
                { name: 'Push-ups', equipment: 'Bodyweight', difficulty: 'Beginner' },
                { name: 'Dips', equipment: 'Bodyweight', difficulty: 'Intermediate' },
                { name: 'Cable Flyes', equipment: 'Cable', difficulty: 'Beginner' },
                { name: 'Pec Deck', equipment: 'Machine', difficulty: 'Beginner' }
            ],
            'Back': [
                { name: 'Deadlift', equipment: 'Barbell', difficulty: 'Advanced' },
                { name: 'Pull-ups', equipment: 'Bodyweight', difficulty: 'Intermediate' },
                { name: 'Chin-ups', equipment: 'Bodyweight', difficulty: 'Intermediate' },
                { name: 'Barbell Rows', equipment: 'Barbell', difficulty: 'Intermediate' },
                { name: 'Dumbbell Rows', equipment: 'Dumbbell', difficulty: 'Beginner' },
                { name: 'Lat Pulldown', equipment: 'Cable', difficulty: 'Beginner' },
                { name: 'Cable Rows', equipment: 'Cable', difficulty: 'Beginner' },
                { name: 'T-Bar Rows', equipment: 'Machine', difficulty: 'Intermediate' }
            ],
            'Shoulders': [
                { name: 'Overhead Press', equipment: 'Barbell', difficulty: 'Intermediate' },
                { name: 'Dumbbell Shoulder Press', equipment: 'Dumbbell', difficulty: 'Beginner' },
                { name: 'Lateral Raises', equipment: 'Dumbbell', difficulty: 'Beginner' },
                { name: 'Front Raises', equipment: 'Dumbbell', difficulty: 'Beginner' },
                { name: 'Rear Delt Flyes', equipment: 'Dumbbell', difficulty: 'Beginner' },
                { name: 'Upright Rows', equipment: 'Barbell', difficulty: 'Intermediate' },
                { name: 'Face Pulls', equipment: 'Cable', difficulty: 'Beginner' },
                { name: 'Arnold Press', equipment: 'Dumbbell', difficulty: 'Intermediate' }
            ],
            'Arms': [
                { name: 'Bicep Curls', equipment: 'Dumbbell', difficulty: 'Beginner' },
                { name: 'Hammer Curls', equipment: 'Dumbbell', difficulty: 'Beginner' },
                { name: 'Barbell Curls', equipment: 'Barbell', difficulty: 'Beginner' },
                { name: 'Tricep Dips', equipment: 'Bodyweight', difficulty: 'Intermediate' },
                { name: 'Close-Grip Bench Press', equipment: 'Barbell', difficulty: 'Intermediate' },
                { name: 'Tricep Extensions', equipment: 'Dumbbell', difficulty: 'Beginner' },
                { name: 'Cable Curls', equipment: 'Cable', difficulty: 'Beginner' },
                { name: 'Tricep Pushdowns', equipment: 'Cable', difficulty: 'Beginner' }
            ],
            'Legs': [
                { name: 'Squats', equipment: 'Barbell', difficulty: 'Intermediate' },
                { name: 'Leg Press', equipment: 'Machine', difficulty: 'Beginner' },
                { name: 'Lunges', equipment: 'Dumbbell', difficulty: 'Beginner' },
                { name: 'Romanian Deadlift', equipment: 'Barbell', difficulty: 'Intermediate' },
                { name: 'Leg Curls', equipment: 'Machine', difficulty: 'Beginner' },
                { name: 'Leg Extensions', equipment: 'Machine', difficulty: 'Beginner' },
                { name: 'Calf Raises', equipment: 'Dumbbell', difficulty: 'Beginner' },
                { name: 'Bulgarian Split Squats', equipment: 'Dumbbell', difficulty: 'Intermediate' }
            ],
            'Core': [
                { name: 'Plank', equipment: 'Bodyweight', difficulty: 'Beginner' },
                { name: 'Crunches', equipment: 'Bodyweight', difficulty: 'Beginner' },
                { name: 'Russian Twists', equipment: 'Bodyweight', difficulty: 'Beginner' },
                { name: 'Mountain Climbers', equipment: 'Bodyweight', difficulty: 'Beginner' },
                { name: 'Dead Bug', equipment: 'Bodyweight', difficulty: 'Beginner' },
                { name: 'Bicycle Crunches', equipment: 'Bodyweight', difficulty: 'Beginner' },
                { name: 'Hanging Leg Raises', equipment: 'Bodyweight', difficulty: 'Advanced' },
                { name: 'Ab Wheel Rollouts', equipment: 'Equipment', difficulty: 'Advanced' }
            ]
        };
    }

    showExerciseSuggestions(query, suggestionsDiv) {
        if (!query || query.length < 2) {
            suggestionsDiv.innerHTML = '';
            suggestionsDiv.style.display = 'none';
            return;
        }

        const suggestions = [];
        Object.values(this.exerciseDatabase).forEach(exercises => {
            exercises.forEach(exercise => {
                if (exercise.name.toLowerCase().includes(query.toLowerCase())) {
                    suggestions.push(exercise);
                }
            });
        });

        if (suggestions.length === 0) {
            suggestionsDiv.innerHTML = '';
            suggestionsDiv.style.display = 'none';
            return;
        }

        suggestionsDiv.innerHTML = suggestions.slice(0, 8).map(exercise => `
            <div class="exercise-suggestion" onclick="workoutManager.selectExercise('${exercise.name}')">
                <div class="exercise-suggestion-name">${exercise.name}</div>
                <div class="exercise-suggestion-details">${exercise.equipment} • ${exercise.difficulty}</div>
            </div>
        `).join('');
        
        suggestionsDiv.style.display = 'block';
    }

    selectExercise(exerciseName) {
        // Add the selected exercise to the current exercise input
        const exerciseInputs = document.querySelectorAll('.exercise-name-input');
        const lastInput = exerciseInputs[exerciseInputs.length - 1];
        
        if (lastInput && !lastInput.value) {
            lastInput.value = exerciseName;
        } else {
            // Add a new exercise input with the selected exercise
            this.addExerciseInput(exerciseName);
        }

        // Hide suggestions
        const suggestionsDiv = document.getElementById('exercise-suggestions');
        if (suggestionsDiv) {
            suggestionsDiv.style.display = 'none';
        }

        // Clear search
        const searchInput = document.getElementById('exercise-search');
        if (searchInput) {
            searchInput.value = '';
        }
    }

    addExerciseInput(exerciseName = '') {
        console.log('addExerciseInput called with enhanced features');
        const exercisesList = document.getElementById('exercises-list');
        const exerciseDiv = document.createElement('div');
        const uniqueId = Date.now() + Math.random();
        exerciseDiv.className = 'exercise-input';

        exerciseDiv.innerHTML = `
            <div class="exercise-header">
                <div class="form-group">
                    <label>Exercise Name</label>
                    <input type="text" placeholder="Exercise name" class="exercise-name-input" value="${exerciseName}" required>
                </div>
                <div class="exercise-controls">
                    <button type="button" class="btn-merge" onclick="workoutManager.toggleExerciseMerge(this)" title="Merge with next exercise">
                        <i class="fas fa-link"></i>
                    </button>
                    <button type="button" class="remove-exercise" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>

            <div class="exercise-type-toggle">
                <label class="toggle-label">
                    <input type="radio" name="type-${uniqueId}" value="reps" class="exercise-type-radio" checked onchange="workoutManager.toggleExerciseType(this)">
                    <span>Reps</span>
                </label>
                <label class="toggle-label">
                    <input type="radio" name="type-${uniqueId}" value="time" class="exercise-type-radio" onchange="workoutManager.toggleExerciseType(this)">
                    <span>Time</span>
                </label>
            </div>

            <div class="exercise-params">
                <div class="form-group">
                    <label>Sets</label>
                    <input type="number" placeholder="Sets" class="exercise-sets-input" min="1" value="3" required>
                </div>
                <div class="form-group reps-group">
                    <label>Reps</label>
                    <input type="number" placeholder="Reps" class="exercise-reps-input" min="1" value="10" required>
                </div>
                <div class="form-group time-group" style="display: none;">
                    <label>Duration (seconds)</label>
                    <input type="number" placeholder="Duration" class="exercise-time-input" min="1" value="60">
                </div>
                <div class="form-group">
                    <label>Weight (kg)</label>
                    <input type="number" placeholder="Weight" class="exercise-weight-input" min="0" step="0.5" value="0">
                </div>
            </div>

            <div class="rest-settings">
                <div class="form-group">
                    <label>Rest Between Sets (seconds)</label>
                    <input type="number" placeholder="Rest time" class="exercise-rest-input" min="30" value="90">
                </div>
            </div>

            <div class="two-set-method">
                <label class="checkbox-label">
                    <input type="checkbox" class="two-set-checkbox">
                    <span>Two-Set Method</span>
                    <small>First set to pressure, second set to failure</small>
                </label>
            </div>
        `;
        exercisesList.appendChild(exerciseDiv);
    }

    toggleExerciseType(radio) {
        const exerciseInput = radio.closest('.exercise-input');
        const repsGroup = exerciseInput.querySelector('.reps-group');
        const timeGroup = exerciseInput.querySelector('.time-group');

        if (radio.value === 'time') {
            repsGroup.style.display = 'none';
            timeGroup.style.display = 'block';
        } else {
            repsGroup.style.display = 'block';
            timeGroup.style.display = 'none';
        }
    }

    toggleExerciseMerge(button) {
        const exerciseInput = button.closest('.exercise-input');
        exerciseInput.classList.toggle('merged');

        if (exerciseInput.classList.contains('merged')) {
            button.innerHTML = '<i class="fas fa-unlink"></i>';
            button.title = 'Unmerge exercise';
        } else {
            button.innerHTML = '<i class="fas fa-link"></i>';
            button.title = 'Merge with next exercise';
        }
    }

    addBreak() {
        const exercisesList = document.getElementById('exercises-list');
        const breakDiv = document.createElement('div');
        breakDiv.className = 'break-input';
        breakDiv.innerHTML = `
            <div class="break-header">
                <i class="fas fa-clock"></i>
                <span>Rest Break</span>
                <button type="button" class="remove-break" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="form-group">
                <label>Break Duration (seconds)</label>
                <input type="number" placeholder="Break duration" class="break-duration-input" min="30" value="120" required>
            </div>
            <div class="form-group">
                <label>Break Note (optional)</label>
                <input type="text" placeholder="e.g., Hydrate, stretch, equipment change" class="break-note-input">
            </div>
        `;
        exercisesList.appendChild(breakDiv);
    }

    saveWorkout() {
        const name = document.getElementById('workout-name').value.trim();
        const description = document.getElementById('workout-description').value.trim();

        if (!name) {
            this.showNotification('Please enter a workout name', 'error');
            return;
        }

        const exerciseInputs = document.querySelectorAll('.exercise-input');
        const breakInputs = document.querySelectorAll('.break-input');
        const workoutItems = [];

        // Combine exercises and breaks in order
        const allItems = [...exerciseInputs, ...breakInputs];
        const sortedItems = Array.from(allItems).sort((a, b) => {
            return Array.from(a.parentNode.children).indexOf(a) - Array.from(b.parentNode.children).indexOf(b);
        });

        sortedItems.forEach(item => {
            if (item.classList.contains('exercise-input')) {
                const exerciseName = item.querySelector('.exercise-name-input').value.trim();
                const sets = parseInt(item.querySelector('.exercise-sets-input').value);
                const typeRadio = item.querySelector('.exercise-type-radio:checked');
                const exerciseType = typeRadio ? typeRadio.value : 'reps';
                const weight = parseFloat(item.querySelector('.exercise-weight-input').value) || 0;
                const restTime = parseInt(item.querySelector('.exercise-rest-input').value) || 90;
                const twoSetMethod = item.querySelector('.two-set-checkbox').checked;
                const isMerged = item.classList.contains('merged');

                let reps = null;
                let duration = null;

                if (exerciseType === 'time') {
                    duration = parseInt(item.querySelector('.exercise-time-input').value);
                } else {
                    reps = parseInt(item.querySelector('.exercise-reps-input').value);
                }

                if (exerciseName && sets && (reps || duration)) {
                    workoutItems.push({
                        type: 'exercise',
                        name: exerciseName,
                        sets: sets,
                        reps: reps,
                        duration: duration,
                        weight: weight,
                        exerciseType: exerciseType,
                        restTime: restTime,
                        twoSetMethod: twoSetMethod,
                        isMerged: isMerged
                    });
                }
            } else if (item.classList.contains('break-input')) {
                const breakDuration = parseInt(item.querySelector('.break-duration-input').value);
                const breakNote = item.querySelector('.break-note-input').value.trim();

                if (breakDuration) {
                    workoutItems.push({
                        type: 'break',
                        duration: breakDuration,
                        note: breakNote
                    });
                }
            }
        });

        if (workoutItems.filter(item => item.type === 'exercise').length === 0) {
            this.showNotification('Please add at least one exercise', 'error');
            return;
        }

        const workout = {
            id: this.currentEditingWorkout ? this.currentEditingWorkout.id : Date.now().toString(),
            name: name,
            description: description,
            items: workoutItems, // Changed from 'exercises' to 'items' to include breaks
            created: this.currentEditingWorkout ? this.currentEditingWorkout.created : new Date().toISOString(),
            modified: new Date().toISOString()
        };

        if (this.currentEditingWorkout) {
            StorageManager.updateWorkout(workout.id, workout);
            this.showNotification('Workout updated successfully!', 'success');
        } else {
            StorageManager.saveWorkout(workout);
            this.showNotification('Workout saved successfully!', 'success');
        }

        this.closeModal();
        this.loadWorkouts();
        this.currentEditingWorkout = null;
    }

    editWorkout(workoutId) {
        const workout = StorageManager.getWorkout(workoutId);
        if (!workout) {
            this.showNotification('Workout not found!', 'error');
            return;
        }

        this.currentEditingWorkout = workout;

        // Open modal and populate with workout data
        document.getElementById('workout-modal').classList.add('active');
        document.getElementById('modal-title').textContent = 'Edit Workout';
        document.getElementById('workout-name').value = workout.name;
        document.getElementById('workout-description').value = workout.description || '';

        // Clear existing exercises
        const exercisesList = document.getElementById('exercises-list');
        exercisesList.innerHTML = '';

        // Handle both old and new formats
        if (workout.items) {
            // New format with items
            workout.items.forEach(item => {
                if (item.type === 'exercise') {
                    this.addExerciseInputWithData(item);
                } else if (item.type === 'break') {
                    this.addBreakWithData(item);
                }
            });
        } else if (workout.exercises) {
            // Old format - convert to new format
            workout.exercises.forEach(exercise => {
                this.addExerciseInputWithData(exercise);
            });
        }
    }

    addExerciseInputWithData(exercise) {
        const exercisesList = document.getElementById('exercises-list');
        const exerciseDiv = document.createElement('div');
        const uniqueId = Date.now() + Math.random();
        exerciseDiv.className = `exercise-input ${exercise.isMerged ? 'merged' : ''}`;

        exerciseDiv.innerHTML = `
            <div class="exercise-header">
                <div class="form-group">
                    <label>Exercise Name</label>
                    <input type="text" placeholder="Exercise name" class="exercise-name-input" value="${exercise.name}" required>
                </div>
                <div class="exercise-controls">
                    <button type="button" class="btn-merge ${exercise.isMerged ? 'active' : ''}" onclick="workoutManager.toggleExerciseMerge(this)" title="${exercise.isMerged ? 'Unmerge exercise' : 'Merge with next exercise'}">
                        <i class="fas fa-${exercise.isMerged ? 'unlink' : 'link'}"></i>
                    </button>
                    <button type="button" class="remove-exercise" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>

            <div class="exercise-type-toggle">
                <label class="toggle-label">
                    <input type="radio" name="type-${uniqueId}" value="reps" class="exercise-type-radio" ${exercise.exerciseType !== 'time' ? 'checked' : ''} onchange="workoutManager.toggleExerciseType(this)">
                    <span>Reps</span>
                </label>
                <label class="toggle-label">
                    <input type="radio" name="type-${uniqueId}" value="time" class="exercise-type-radio" ${exercise.exerciseType === 'time' ? 'checked' : ''} onchange="workoutManager.toggleExerciseType(this)">
                    <span>Time</span>
                </label>
            </div>

            <div class="exercise-params">
                <div class="form-group">
                    <label>Sets</label>
                    <input type="number" placeholder="Sets" class="exercise-sets-input" min="1" value="${exercise.sets}" required>
                </div>
                <div class="form-group reps-group" style="display: ${exercise.exerciseType === 'time' ? 'none' : 'block'};">
                    <label>Reps</label>
                    <input type="number" placeholder="Reps" class="exercise-reps-input" min="1" value="${exercise.reps || 10}" required>
                </div>
                <div class="form-group time-group" style="display: ${exercise.exerciseType === 'time' ? 'block' : 'none'};">
                    <label>Duration (seconds)</label>
                    <input type="number" placeholder="Duration" class="exercise-time-input" min="1" value="${exercise.duration || 60}">
                </div>
                <div class="form-group">
                    <label>Weight (kg)</label>
                    <input type="number" placeholder="Weight" class="exercise-weight-input" min="0" step="0.5" value="${exercise.weight || 0}">
                </div>
            </div>

            <div class="two-set-method">
                <label class="checkbox-label">
                    <input type="checkbox" class="two-set-checkbox" ${exercise.twoSetMethod ? 'checked' : ''}>
                    <span>Two-Set Method</span>
                    <small>First set to pressure, second set to failure</small>
                </label>
            </div>

            <div class="rest-settings">
                <div class="form-group">
                    <label>Rest Between Sets (seconds)</label>
                    <input type="number" placeholder="Rest time" class="exercise-rest-input" min="30" value="${exercise.restTime || 90}">
                </div>
            </div>
        `;
        exercisesList.appendChild(exerciseDiv);
    }

    addBreakWithData(breakData) {
        const exercisesList = document.getElementById('exercises-list');
        const breakDiv = document.createElement('div');
        breakDiv.className = 'break-input';
        breakDiv.innerHTML = `
            <div class="break-header">
                <i class="fas fa-clock"></i>
                <span>Rest Break</span>
                <button type="button" class="remove-break" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="form-group">
                <label>Break Duration (seconds)</label>
                <input type="number" placeholder="Break duration" class="break-duration-input" min="30" value="${breakData.duration}" required>
            </div>
            <div class="form-group">
                <label>Break Note (optional)</label>
                <input type="text" placeholder="e.g., Hydrate, stretch, equipment change" class="break-note-input" value="${breakData.note || ''}">
            </div>
        `;
        exercisesList.appendChild(breakDiv);
    }

    deleteWorkout(workoutId) {
        if (confirm('Are you sure you want to delete this workout? This action cannot be undone.')) {
            StorageManager.deleteWorkout(workoutId);
            this.loadWorkouts();
            this.showNotification('Workout deleted successfully!', 'success');
        }
    }

    duplicateWorkout(workoutId) {
        const workout = StorageManager.getWorkout(workoutId);
        if (!workout) {
            this.showNotification('Workout not found!', 'error');
            return;
        }

        const duplicatedWorkout = {
            ...workout,
            id: Date.now().toString(),
            name: workout.name + ' (Copy)',
            created: new Date().toISOString(),
            modified: new Date().toISOString()
        };

        StorageManager.saveWorkout(duplicatedWorkout);
        this.loadWorkouts();
        this.showNotification('Workout duplicated successfully!', 'success');
    }

    loadWorkouts() {
        const workouts = StorageManager.getWorkouts();
        const workoutsList = document.getElementById('workouts-list');
        
        if (!workoutsList) return;

        workoutsList.innerHTML = '';

        if (workouts.length === 0) {
            workoutsList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-dumbbell"></i>
                    <h3>No workouts created yet</h3>
                    <p>Create your first workout to get started!</p>
                    <button class="btn btn-primary" onclick="app.openWorkoutModal()">
                        <i class="fas fa-plus"></i>
                        Create Workout
                    </button>
                </div>
            `;
            return;
        }

        workouts.forEach(workout => {
            const workoutCard = document.createElement('div');
            workoutCard.className = 'workout-card';

            // Handle both old and new formats
            let exercises = [];
            let breaks = [];
            let totalVolume = 0;
            let totalSets = 0;

            if (workout.items) {
                // New format
                exercises = workout.items.filter(item => item.type === 'exercise');
                breaks = workout.items.filter(item => item.type === 'break');
                totalVolume = exercises.reduce((sum, exercise) => {
                    const weight = exercise.weight || 0;
                    const reps = exercise.reps || 0;
                    const sets = exercise.sets || 0;
                    return sum + (weight * reps * sets);
                }, 0);
                totalSets = exercises.reduce((sum, ex) => sum + (ex.sets || 0), 0);
            } else if (workout.exercises) {
                // Old format
                exercises = workout.exercises;
                totalVolume = exercises.reduce((sum, exercise) => {
                    return sum + ((exercise.weight || 0) * (exercise.reps || 0) * (exercise.sets || 0));
                }, 0);
                totalSets = exercises.reduce((sum, ex) => sum + (ex.sets || 0), 0);
            }

            workoutCard.innerHTML = `
                <div class="workout-card-header">
                    <h3>${workout.name}</h3>
                    <div class="workout-card-menu">
                        <button class="menu-btn" onclick="this.nextElementSibling.classList.toggle('show')">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div class="dropdown-menu">
                            <button onclick="workoutManager.duplicateWorkout('${workout.id}')">
                                <i class="fas fa-copy"></i> Duplicate
                            </button>
                            <button onclick="workoutManager.exportWorkout('${workout.id}')">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                </div>
                <p class="workout-description">${workout.description || 'No description'}</p>
                <div class="workout-stats">
                    <div class="stat">
                        <span class="stat-value">${exercises.length}</span>
                        <span class="stat-label">Exercises</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">${totalSets}</span>
                        <span class="stat-label">Sets</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">${Math.round(totalVolume)}kg</span>
                        <span class="stat-label">Volume</span>
                    </div>
                    ${breaks.length > 0 ? `
                    <div class="stat">
                        <span class="stat-value">${breaks.length}</span>
                        <span class="stat-label">Breaks</span>
                    </div>
                    ` : ''}
                </div>
                <div class="workout-exercises">
                    ${exercises.slice(0, 3).map(exercise => {
                        let badges = '';
                        if (exercise.exerciseType === 'time') badges += ' <span class="mini-badge">TIME</span>';
                        if (exercise.twoSetMethod) badges += ' <span class="mini-badge">2-SET</span>';
                        if (exercise.isMerged) badges += ' <span class="mini-badge">MERGED</span>';
                        return `<span class="exercise-tag">${exercise.name}${badges}</span>`;
                    }).join('')}
                    ${exercises.length > 3 ? `<span class="exercise-tag more">+${exercises.length - 3} more</span>` : ''}
                </div>
                <div class="workout-actions">
                    <button class="btn btn-primary" onclick="app.startWorkout('${workout.id}')">
                        <i class="fas fa-play"></i>
                        Start
                    </button>
                    <button class="btn btn-secondary" onclick="workoutManager.editWorkout('${workout.id}')">
                        <i class="fas fa-edit"></i>
                        Edit
                    </button>
                    <button class="btn btn-danger" onclick="workoutManager.deleteWorkout('${workout.id}')">
                        <i class="fas fa-trash"></i>
                        Delete
                    </button>
                </div>
            `;
            workoutsList.appendChild(workoutCard);
        });
    }

    exportWorkout(workoutId) {
        const workout = StorageManager.getWorkout(workoutId);
        if (!workout) {
            this.showNotification('Workout not found!', 'error');
            return;
        }

        const workoutData = JSON.stringify(workout, null, 2);
        const blob = new Blob([workoutData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${workout.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showNotification('Workout exported successfully!', 'success');
    }

    importWorkout(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const workout = JSON.parse(e.target.result);
                
                // Validate workout structure
                if (!workout.name || !workout.exercises || !Array.isArray(workout.exercises)) {
                    throw new Error('Invalid workout format');
                }

                // Generate new ID and timestamps
                workout.id = Date.now().toString();
                workout.created = new Date().toISOString();
                workout.modified = new Date().toISOString();

                StorageManager.saveWorkout(workout);
                this.loadWorkouts();
                this.showNotification('Workout imported successfully!', 'success');
            } catch (error) {
                this.showNotification('Error importing workout: Invalid file format', 'error');
            }
        };
        reader.readAsText(file);
    }

    closeModal() {
        document.getElementById('workout-modal').classList.remove('active');
        this.currentEditingWorkout = null;
        
        // Clear search
        const searchInput = document.getElementById('exercise-search');
        if (searchInput) {
            searchInput.value = '';
        }
        
        const suggestionsDiv = document.getElementById('exercise-suggestions');
        if (suggestionsDiv) {
            suggestionsDiv.style.display = 'none';
        }
    }

    showNotification(message, type = 'info') {
        if (window.timerManager) {
            window.timerManager.showInAppNotification('Workout Manager', message, type);
        } else {
            alert(message);
        }
    }

    // Workout Templates
    createWorkoutTemplate(type) {
        const templates = {
            'push': {
                name: 'Push Day',
                description: 'Chest, shoulders, and triceps workout',
                exercises: [
                    { name: 'Bench Press', sets: 4, reps: 8, weight: 0 },
                    { name: 'Incline Dumbbell Press', sets: 3, reps: 10, weight: 0 },
                    { name: 'Overhead Press', sets: 3, reps: 8, weight: 0 },
                    { name: 'Lateral Raises', sets: 3, reps: 12, weight: 0 },
                    { name: 'Tricep Dips', sets: 3, reps: 10, weight: 0 },
                    { name: 'Tricep Extensions', sets: 3, reps: 12, weight: 0 }
                ]
            },
            'pull': {
                name: 'Pull Day',
                description: 'Back and biceps workout',
                exercises: [
                    { name: 'Deadlift', sets: 4, reps: 6, weight: 0 },
                    { name: 'Pull-ups', sets: 3, reps: 8, weight: 0 },
                    { name: 'Barbell Rows', sets: 3, reps: 8, weight: 0 },
                    { name: 'Lat Pulldown', sets: 3, reps: 10, weight: 0 },
                    { name: 'Barbell Curls', sets: 3, reps: 10, weight: 0 },
                    { name: 'Hammer Curls', sets: 3, reps: 12, weight: 0 }
                ]
            },
            'legs': {
                name: 'Leg Day',
                description: 'Lower body workout',
                exercises: [
                    { name: 'Squats', sets: 4, reps: 8, weight: 0 },
                    { name: 'Romanian Deadlift', sets: 3, reps: 10, weight: 0 },
                    { name: 'Leg Press', sets: 3, reps: 12, weight: 0 },
                    { name: 'Leg Curls', sets: 3, reps: 12, weight: 0 },
                    { name: 'Calf Raises', sets: 4, reps: 15, weight: 0 },
                    { name: 'Lunges', sets: 3, reps: 10, weight: 0 }
                ]
            }
        };

        const template = templates[type];
        if (template) {
            const workout = {
                ...template,
                id: Date.now().toString(),
                created: new Date().toISOString(),
                modified: new Date().toISOString()
            };

            StorageManager.saveWorkout(workout);
            this.loadWorkouts();
            this.showNotification(`${template.name} template created!`, 'success');
        }
    }
}

// Create global workout manager instance
window.workoutManager = new WorkoutManager();
console.log('WorkoutManager initialized with enhanced features');
