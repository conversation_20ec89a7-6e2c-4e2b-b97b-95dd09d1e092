# 2Set Tracker - Workout Tracker Web Application

A comprehensive workout tracking web application designed for fitness enthusiasts who want to monitor their progress, track time under tension, and analyze their workout data over time.

## Features

### 🏋️ Workout Management
- Create custom workouts with multiple exercises
- Exercise database with suggestions for common movements
- Edit and duplicate existing workouts
- Import/export workout templates

### ⏱️ Time Tracking
- **Workout Timer**: Track total workout duration
- **Exercise Timers**: Monitor time spent on each exercise
- **Rest Timer**: Customizable rest periods between sets with visual countdown
- **Time Under Tension (TUT)**: Track muscle tension time for each set

### 📊 Progress Analytics
- Week-by-week progress tracking
- Volume and strength progression charts
- Personal record tracking with 1RM calculations
- Intelligent recommendations for weight increases and exercise progressions

### 📱 User Experience
- Responsive design optimized for mobile and desktop
- Intuitive dashboard with workout statistics
- Real-time notifications and alerts
- Offline functionality with local storage

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Charts**: Chart.js for progress visualization
- **Storage**: Browser LocalStorage for data persistence
- **Icons**: Font Awesome
- **Hosting**: Compatible with <PERSON><PERSON> and other web hosts

## Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- No server-side requirements - runs entirely in the browser

### Installation
1. Download or clone the project files
2. Upload all files to your web hosting service (Hostinger, etc.)
3. Navigate to your domain to start using the application

### Local Development
1. Clone the repository
2. Open `index.html` in your web browser
3. Start creating workouts and tracking your progress!

## File Structure

```
2settracker/
├── index.html              # Main application page
├── css/
│   └── styles.css          # All application styles
├── js/
│   ├── app.js              # Main application controller
│   ├── storage.js          # Data persistence and analytics
│   ├── timer.js            # Timer functionality
│   ├── progress.js         # Progress tracking and charts
│   └── workouts.js         # Workout management
└── README.md               # This file
```

## Usage Guide

### Creating Your First Workout
1. Click "Create New Workout" from the dashboard
2. Enter workout name and description
3. Add exercises using the search feature or manual entry
4. Set default sets, reps, and weights for each exercise
5. Save your workout

### Starting a Workout
1. Go to the "Workouts" tab
2. Click "Start" on any workout
3. The active workout timer will begin automatically
4. For each set:
   - Enter the weight used
   - Enter reps completed
   - Use TUT timer if tracking time under tension
   - Check the set as complete
   - Start rest timer between sets

### Tracking Progress
1. Visit the "Progress" tab to view analytics
2. Switch between week, month, and year views
3. Review volume and strength progression charts
4. Check recommendations for workout improvements

## Data Management

### Local Storage
All data is stored locally in your browser using LocalStorage:
- Workout templates
- Completed workout sessions
- User settings and preferences

### Data Export/Import
- Export individual workouts as JSON files
- Generate progress reports as text files
- Import workout templates from other users

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Hosting on Hostinger

1. Log into your Hostinger control panel
2. Navigate to File Manager
3. Upload all project files to the `public_html` directory
4. Ensure `index.html` is in the root directory
5. Your workout tracker will be available at your domain

## Features in Detail

### Time Under Tension (TUT)
- Click the stopwatch button to start timing muscle tension
- Automatically populates the TUT field when stopped
- Helps optimize muscle building and strength gains

### Rest Timer
- Customizable rest periods (default 60 seconds)
- Visual countdown with progress bar
- Audio notification when rest is complete
- Add/subtract 30 seconds during rest

### Progress Recommendations
The app analyzes your workout data to provide intelligent suggestions:
- When to increase weights
- Exercise progression recommendations
- Workout frequency optimization
- Muscle group balance analysis

### Workout Statistics
Track comprehensive metrics:
- Total volume (weight × reps × sets)
- Workout frequency and consistency
- Personal records and 1RM estimates
- Time-based analytics

## Customization

### Adding New Exercises
Edit the `exerciseDatabase` in `js/workouts.js` to add new exercises to the suggestion system.

### Modifying Rest Timer Defaults
Change the default rest time in `js/storage.js` under the settings configuration.

### Styling Customization
All visual styling is contained in `css/styles.css` and can be modified to match your preferences.

## Support

For issues or feature requests, please check the code comments and documentation within each JavaScript file. The application is designed to be self-contained and easy to modify.

## License

This project is open source and available for personal and commercial use.

---

**Start tracking your workouts today and achieve your fitness goals with data-driven insights!**
