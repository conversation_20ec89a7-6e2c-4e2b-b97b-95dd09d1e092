// Storage Manager for handling data persistence
class StorageManager {
    static KEYS = {
        WORKOUTS: 'workout_tracker_workouts',
        SESSIONS: 'workout_tracker_sessions',
        SETTINGS: 'workout_tracker_settings'
    };

    // Workout Templates Management
    static saveWorkout(workout) {
        const workouts = this.getWorkouts();

        // Ensure workout has proper structure
        if (workout.items) {
            // New format - ensure backward compatibility
            workout.exercises = workout.items.filter(item => item.type === 'exercise');
        } else if (workout.exercises && !workout.items) {
            // Old format - convert to new format
            workout.items = workout.exercises.map(exercise => ({
                type: 'exercise',
                ...exercise,
                exerciseType: exercise.exerciseType || 'reps',
                restTime: exercise.restTime || 90,
                twoSetMethod: exercise.twoSetMethod || false,
                isMerged: exercise.isMerged || false
            }));
        }

        const existingIndex = workouts.findIndex(w => w.id === workout.id);
        if (existingIndex !== -1) {
            workouts[existingIndex] = workout;
        } else {
            workouts.push(workout);
        }

        localStorage.setItem(this.KEYS.WORKOUTS, JSON.stringify(workouts));
    }

    static getWorkouts() {
        const workouts = localStorage.getItem(this.KEYS.WORKOUTS);
        return workouts ? JSON.parse(workouts) : [];
    }

    static updateWorkout(workoutId, updatedWorkout) {
        const workouts = this.getWorkouts();
        const index = workouts.findIndex(w => w.id === workoutId);
        if (index !== -1) {
            workouts[index] = { ...workouts[index], ...updatedWorkout };
            localStorage.setItem(this.KEYS.WORKOUTS, JSON.stringify(workouts));
            return true;
        }
        return false;
    }

    static deleteWorkout(workoutId) {
        const workouts = this.getWorkouts();
        const filteredWorkouts = workouts.filter(w => w.id !== workoutId);
        localStorage.setItem(this.KEYS.WORKOUTS, JSON.stringify(filteredWorkouts));
    }

    static getWorkout(workoutId) {
        const workouts = this.getWorkouts();
        return workouts.find(w => w.id === workoutId);
    }

    // Workout Sessions Management
    static saveWorkoutSession(session) {
        const sessions = this.getWorkoutSessions();
        sessions.push(session);
        localStorage.setItem(this.KEYS.SESSIONS, JSON.stringify(sessions));
    }

    static getWorkoutSessions() {
        const sessions = localStorage.getItem(this.KEYS.SESSIONS);
        return sessions ? JSON.parse(sessions) : [];
    }

    static getWorkoutSessionsByDateRange(startDate, endDate) {
        const sessions = this.getWorkoutSessions();
        return sessions.filter(session => {
            const sessionDate = new Date(session.date);
            return sessionDate >= startDate && sessionDate <= endDate;
        });
    }

    static getRecentWorkouts(limit = 5) {
        const sessions = this.getWorkoutSessions();
        return sessions
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, limit);
    }

    // Statistics and Analytics
    static getWeeklyStats() {
        const now = new Date();
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        startOfWeek.setHours(0, 0, 0, 0);

        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        endOfWeek.setHours(23, 59, 59, 999);

        const weekSessions = this.getWorkoutSessionsByDateRange(startOfWeek, endOfWeek);
        
        const totalTime = weekSessions.reduce((sum, session) => sum + (session.duration || 0), 0);
        const workoutsThisWeek = weekSessions.length;
        const streak = this.calculateStreak();

        return {
            workoutsThisWeek,
            totalTime: totalTime / 1000, // Convert to seconds
            streak
        };
    }

    static calculateStreak() {
        const sessions = this.getWorkoutSessions();
        if (sessions.length === 0) return 0;

        // Sort sessions by date (newest first)
        const sortedSessions = sessions.sort((a, b) => new Date(b.date) - new Date(a.date));
        
        // Get unique workout dates
        const workoutDates = [...new Set(sortedSessions.map(session => {
            const date = new Date(session.date);
            return date.toDateString();
        }))];

        let streak = 0;
        const today = new Date();
        
        for (let i = 0; i < workoutDates.length; i++) {
            const workoutDate = new Date(workoutDates[i]);
            const daysDiff = Math.floor((today - workoutDate) / (1000 * 60 * 60 * 24));
            
            if (i === 0 && daysDiff <= 1) {
                streak = 1;
            } else if (i > 0) {
                const prevWorkoutDate = new Date(workoutDates[i - 1]);
                const daysBetween = Math.floor((prevWorkoutDate - workoutDate) / (1000 * 60 * 60 * 24));
                
                if (daysBetween === 1) {
                    streak++;
                } else {
                    break;
                }
            } else {
                break;
            }
        }

        return streak;
    }

    static getProgressData(period = 'week') {
        const now = new Date();
        let startDate, endDate;

        switch (period) {
            case 'week':
                startDate = new Date(now);
                startDate.setDate(now.getDate() - 7);
                endDate = now;
                break;
            case 'month':
                startDate = new Date(now);
                startDate.setMonth(now.getMonth() - 1);
                endDate = now;
                break;
            case 'year':
                startDate = new Date(now);
                startDate.setFullYear(now.getFullYear() - 1);
                endDate = now;
                break;
            default:
                startDate = new Date(now);
                startDate.setDate(now.getDate() - 7);
                endDate = now;
        }

        const sessions = this.getWorkoutSessionsByDateRange(startDate, endDate);
        return this.analyzeProgressData(sessions, period);
    }

    static analyzeProgressData(sessions, period) {
        const exerciseProgress = new Map();
        const volumeData = [];
        const strengthData = [];

        // Group sessions by date for volume tracking
        const sessionsByDate = new Map();
        
        sessions.forEach(session => {
            const dateKey = new Date(session.date).toDateString();
            if (!sessionsByDate.has(dateKey)) {
                sessionsByDate.set(dateKey, []);
            }
            sessionsByDate.get(dateKey).push(session);

            // Track exercise progress
            session.exercises.forEach(exercise => {
                if (!exerciseProgress.has(exercise.name)) {
                    exerciseProgress.set(exercise.name, []);
                }

                const exerciseData = {
                    date: session.date,
                    sets: exercise.sets,
                    totalVolume: 0,
                    maxWeight: 0,
                    totalReps: 0
                };

                exercise.sets.forEach(set => {
                    if (set) {
                        const weight = parseFloat(set.weight) || 0;
                        const reps = parseInt(set.reps) || 0;
                        exerciseData.totalVolume += weight * reps;
                        exerciseData.maxWeight = Math.max(exerciseData.maxWeight, weight);
                        exerciseData.totalReps += reps;
                    }
                });

                exerciseProgress.get(exercise.name).push(exerciseData);
            });
        });

        // Calculate daily volume
        sessionsByDate.forEach((daySessions, dateKey) => {
            let totalVolume = 0;
            let totalSets = 0;

            daySessions.forEach(session => {
                session.exercises.forEach(exercise => {
                    exercise.sets.forEach(set => {
                        if (set) {
                            const weight = parseFloat(set.weight) || 0;
                            const reps = parseInt(set.reps) || 0;
                            totalVolume += weight * reps;
                            totalSets++;
                        }
                    });
                });
            });

            volumeData.push({
                date: dateKey,
                volume: totalVolume,
                sets: totalSets
            });
        });

        // Calculate strength progression for major exercises
        const majorExercises = ['Bench Press', 'Squat', 'Deadlift', 'Overhead Press'];
        majorExercises.forEach(exerciseName => {
            if (exerciseProgress.has(exerciseName)) {
                const exerciseData = exerciseProgress.get(exerciseName);
                const strengthProgression = exerciseData.map(data => ({
                    date: data.date,
                    maxWeight: data.maxWeight,
                    exercise: exerciseName
                }));
                strengthData.push(...strengthProgression);
            }
        });

        return {
            volumeData: volumeData.sort((a, b) => new Date(a.date) - new Date(b.date)),
            strengthData: strengthData.sort((a, b) => new Date(a.date) - new Date(b.date)),
            exerciseProgress: Object.fromEntries(exerciseProgress)
        };
    }

    static generateRecommendations() {
        const sessions = this.getWorkoutSessions();
        const recommendations = [];

        if (sessions.length === 0) {
            return [{
                type: 'getting-started',
                title: 'Start Your Fitness Journey',
                message: 'Create your first workout and begin tracking your progress!',
                icon: 'fas fa-rocket'
            }];
        }

        const recentSessions = sessions
            .filter(session => {
                const sessionDate = new Date(session.date);
                const weekAgo = new Date();
                weekAgo.setDate(weekAgo.getDate() - 7);
                return sessionDate >= weekAgo;
            });

        // Check workout frequency
        if (recentSessions.length < 3) {
            recommendations.push({
                type: 'frequency',
                title: 'Increase Workout Frequency',
                message: 'Try to workout at least 3 times per week for optimal results.',
                icon: 'fas fa-calendar-plus'
            });
        }

        // Analyze exercise progression
        const exerciseProgress = this.analyzeExerciseProgression(sessions);
        exerciseProgress.forEach(progress => {
            if (progress.shouldIncrease) {
                recommendations.push({
                    type: 'progression',
                    title: `Increase ${progress.exercise} Weight`,
                    message: `You've been consistent with ${progress.exercise}. Consider increasing the weight by 5-10%.`,
                    icon: 'fas fa-arrow-up'
                });
            }
        });

        // Check for muscle group balance
        const muscleGroupBalance = this.analyzeMuscleGroupBalance(recentSessions);
        if (muscleGroupBalance.imbalanced.length > 0) {
            recommendations.push({
                type: 'balance',
                title: 'Balance Your Training',
                message: `Consider adding more ${muscleGroupBalance.imbalanced.join(', ')} exercises to your routine.`,
                icon: 'fas fa-balance-scale'
            });
        }

        return recommendations.slice(0, 5); // Limit to 5 recommendations
    }

    static analyzeExerciseProgression(sessions) {
        const exerciseData = new Map();
        const progressions = [];

        // Collect exercise data from recent sessions
        sessions.slice(-10).forEach(session => { // Last 10 sessions
            session.exercises.forEach(exercise => {
                if (!exerciseData.has(exercise.name)) {
                    exerciseData.set(exercise.name, []);
                }

                const maxWeight = Math.max(...exercise.sets
                    .filter(set => set && set.weight)
                    .map(set => parseFloat(set.weight)));

                if (maxWeight > 0) {
                    exerciseData.get(exercise.name).push({
                        date: session.date,
                        maxWeight: maxWeight
                    });
                }
            });
        });

        // Analyze progression for each exercise
        exerciseData.forEach((data, exerciseName) => {
            if (data.length >= 3) {
                const recent = data.slice(-3);
                const weights = recent.map(d => d.maxWeight);
                const isStagnant = weights.every(w => w === weights[0]);
                const hasProgressed = weights[weights.length - 1] > weights[0];

                progressions.push({
                    exercise: exerciseName,
                    shouldIncrease: isStagnant && !hasProgressed,
                    currentWeight: weights[weights.length - 1],
                    trend: hasProgressed ? 'increasing' : isStagnant ? 'stagnant' : 'decreasing'
                });
            }
        });

        return progressions;
    }

    static analyzeMuscleGroupBalance(sessions) {
        const muscleGroups = {
            'Push': ['Push-up', 'Bench Press', 'Overhead Press', 'Dips'],
            'Pull': ['Pull-up', 'Row', 'Lat Pulldown', 'Chin-up'],
            'Legs': ['Squat', 'Deadlift', 'Lunge', 'Leg Press'],
            'Core': ['Plank', 'Crunch', 'Russian Twist', 'Mountain Climber']
        };

        const groupCounts = {
            'Push': 0,
            'Pull': 0,
            'Legs': 0,
            'Core': 0
        };

        sessions.forEach(session => {
            session.exercises.forEach(exercise => {
                Object.keys(muscleGroups).forEach(group => {
                    if (muscleGroups[group].some(ex => 
                        exercise.name.toLowerCase().includes(ex.toLowerCase()))) {
                        groupCounts[group]++;
                    }
                });
            });
        });

        const total = Object.values(groupCounts).reduce((sum, count) => sum + count, 0);
        const imbalanced = [];

        if (total > 0) {
            Object.keys(groupCounts).forEach(group => {
                const percentage = (groupCounts[group] / total) * 100;
                if (percentage < 15) { // Less than 15% of total exercises
                    imbalanced.push(group.toLowerCase());
                }
            });
        }

        return { groupCounts, imbalanced };
    }

    // Settings Management
    static saveSettings(settings) {
        localStorage.setItem(this.KEYS.SETTINGS, JSON.stringify(settings));
    }

    static getSettings() {
        const settings = localStorage.getItem(this.KEYS.SETTINGS);
        return settings ? JSON.parse(settings) : {
            units: 'metric', // metric or imperial
            restTimer: 60, // seconds
            autoAdvance: true,
            soundEnabled: true
        };
    }

    // Data Export/Import
    static exportData() {
        const data = {
            workouts: this.getWorkouts(),
            sessions: this.getWorkoutSessions(),
            settings: this.getSettings(),
            exportDate: new Date().toISOString()
        };
        return JSON.stringify(data, null, 2);
    }

    static importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (data.workouts) {
                localStorage.setItem(this.KEYS.WORKOUTS, JSON.stringify(data.workouts));
            }
            
            if (data.sessions) {
                localStorage.setItem(this.KEYS.SESSIONS, JSON.stringify(data.sessions));
            }
            
            if (data.settings) {
                localStorage.setItem(this.KEYS.SETTINGS, JSON.stringify(data.settings));
            }
            
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }

    // Clear all data
    static clearAllData() {
        Object.values(this.KEYS).forEach(key => {
            localStorage.removeItem(key);
        });
    }
}

// Make StorageManager globally available
window.StorageManager = StorageManager;
