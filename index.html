<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2Set Tracker - Workout Tracker</title>
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="stylesheet" href="css/styles.css?v=6">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- Navigation -->
        <nav class="navbar">
            <div class="nav-container">
                <h1 class="nav-logo">2Set Tracker</h1>
                <div class="nav-menu">
                    <button class="nav-btn active" data-tab="dashboard">
                        <i class="fas fa-home"></i>
                        <span>Dashboard</span>
                    </button>
                    <button class="nav-btn" data-tab="workouts">
                        <i class="fas fa-dumbbell"></i>
                        <span>Workouts</span>
                    </button>
                    <button class="nav-btn" data-tab="active-workout">
                        <i class="fas fa-play"></i>
                        <span>Active</span>
                    </button>
                    <button class="nav-btn" data-tab="progress">
                        <i class="fas fa-chart-line"></i>
                        <span>Progress</span>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Tab -->
            <div id="dashboard" class="tab-content active">
                <div class="dashboard-header">
                    <h2>Welcome Back!</h2>
                    <p class="date" id="current-date"></p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <i class="fas fa-calendar-week"></i>
                        <div class="stat-info">
                            <h3 id="workouts-this-week">0</h3>
                            <p>Workouts This Week</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-clock"></i>
                        <div class="stat-info">
                            <h3 id="total-time">0m</h3>
                            <p>Total Time This Week</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-fire"></i>
                        <div class="stat-info">
                            <h3 id="streak-count">0</h3>
                            <p>Day Streak</p>
                        </div>
                    </div>
                </div>

                <div class="recent-workouts">
                    <h3>Recent Workouts</h3>
                    <div id="recent-workouts-list" class="workout-list">
                        <!-- Recent workouts will be populated here -->
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="btn btn-primary" id="start-quick-workout">
                        <i class="fas fa-play"></i>
                        Start Quick Workout
                    </button>
                    <button class="btn btn-secondary" id="create-new-workout">
                        <i class="fas fa-plus"></i>
                        Create New Workout
                    </button>
                </div>
            </div>

            <!-- Workouts Tab -->
            <div id="workouts" class="tab-content">
                <div class="workouts-header">
                    <h2>My Workouts</h2>
                    <button class="btn btn-primary" id="add-workout-btn">
                        <i class="fas fa-plus"></i>
                        Add Workout
                    </button>
                </div>
                
                <div id="workouts-list" class="workouts-grid">
                    <!-- Workouts will be populated here -->
                </div>
            </div>

            <!-- Active Workout Tab -->
            <div id="active-workout" class="tab-content">
                <div class="workout-header">
                    <h2 id="active-workout-name">Select a Workout</h2>
                    <div class="workout-timer">
                        <span id="workout-time">00:00</span>
                    </div>
                </div>

                <div id="exercise-container" class="exercise-container">
                    <!-- Active workout exercises will be populated here -->
                </div>

                <div class="workout-controls">
                    <button class="btn btn-secondary" id="pause-workout">
                        <i class="fas fa-pause"></i>
                        Pause
                    </button>
                    <button class="btn btn-success" id="finish-workout">
                        <i class="fas fa-check"></i>
                        Finish Workout
                    </button>
                </div>
            </div>

            <!-- Progress Tab -->
            <div id="progress" class="tab-content">
                <div class="progress-header">
                    <h2>Progress Tracking</h2>
                    <div class="time-filter">
                        <button class="filter-btn active" data-period="week">Week</button>
                        <button class="filter-btn" data-period="month">Month</button>
                        <button class="filter-btn" data-period="year">Year</button>
                    </div>
                </div>

                <div class="progress-stats">
                    <div class="progress-card">
                        <h3>Volume Progress</h3>
                        <canvas id="volume-chart"></canvas>
                    </div>
                    <div class="progress-card">
                        <h3>Strength Progress</h3>
                        <canvas id="strength-chart"></canvas>
                    </div>
                </div>

                <div class="recommendations">
                    <h3>Recommendations</h3>
                    <div id="recommendations-list">
                        <!-- Recommendations will be populated here -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="workout-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Create New Workout</h3>
                <button class="close-btn" id="close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="workout-form">
                    <div class="form-group">
                        <label for="workout-name">Workout Name</label>
                        <input type="text" id="workout-name" required>
                    </div>
                    <div class="form-group">
                        <label for="workout-description">Description</label>
                        <textarea id="workout-description" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Exercises</label>
                        <div id="exercises-list">
                            <!-- Exercise inputs will be added here -->
                        </div>
                        <div class="workout-builder-actions">
                            <button type="button" class="btn btn-secondary" id="add-exercise">
                                <i class="fas fa-plus"></i>
                                Add Exercise
                            </button>
                            <button type="button" class="btn btn-warning" onclick="workoutManager.addBreak()">
                                <i class="fas fa-clock"></i>
                                Add Break
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancel-workout">Cancel</button>
                <button class="btn btn-primary" id="save-workout">Save Workout</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/app.js?v=3"></script>
    <script src="js/workouts.js?v=6"></script>
    <script src="js/timer.js?v=3"></script>
    <script src="js/progress.js?v=3"></script>
    <script src="js/storage.js?v=3"></script>
</body>
</html>
